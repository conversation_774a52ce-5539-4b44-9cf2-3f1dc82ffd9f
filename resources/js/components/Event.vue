<template>
	<div class="container-fluid fixed-bottom d-flex justify-content-end support_btn" v-if="livechat && event.allow_msg && checkLogin() && user">
		<button class="mb-5 bottom-0 end-0 w-100px btn btn-primary" type="button" data-bs-toggle="offcanvas"
			data-bs-target="#offcanvasRight" aria-controls="offcanvasRight">{{ translates.Support }}</button>
	</div>

	<div class="offcanvas offcanvas-end h-75" tabindex="-1" id="offcanvasRight" aria-labelledby="offcanvasRightLabel" v-if="livechat && event.allow_msg && checkLogin() && user">
		<div class="offcanvas-body ms-3 me-3 p-0">
			<!--begin::Messenger-->
			<div class="card w-100 rounded-0 border-0 mb-5" id="kt_drawer_chat_messenger">
				<!--begin::Card header-->
				<div class="bg-white position-fixed w-100 mb-10 z-index-1 card-header p-0" id="kt_drawer_chat_messenger_header">
					<!--begin::Title-->
					<div class="card-title d-flex justify-content-between">
						<!--begin::User-->
						<div class="d-flex justify-content-center flex-column">
							<a href="javascript:void(0)" class="fs-4 fw-bold text-gray-900 text-hover-primary me-1 mb-2 lh-1">{{ translates.Support_Chat }}</a>
						</div>
						<!--end::User-->
						<!--begin::Close-->
						<div type="ms-5 button" id="close-btn" class="btn-sm btn-icon btn-active-light-primary btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"><span class="svg-icon svg-icon-2">
						</span></div>
					</div>
					<!--end::Title-->
				</div>
				<!--end::Card header-->
				<!--begin::Card body-->
				<div class="card-body mt-15 mb-15 p-0" id="kt_drawer_chat_messenger_body">
					<!--begin::Messages-->
					<div id="message_div" class="mt-10 scroll-y ms-0 ps-0" data-kt-element="messages" data-kt-scroll="true"
						data-kt-scroll-activate="true" data-kt-scroll-height="auto"
						data-kt-scroll-dependencies="#kt_drawer_chat_messenger_header, #kt_drawer_chat_messenger_footer"
						data-kt-scroll-wrappers="#kt_drawer_chat_messenger_body" data-kt-scroll-offset="0px" ref="hasScrolledToBottom">
						<div v-if="messages.length">
							<div v-for="message in messages" :key="message.id" class="message_div">
								<!--begin::Message(in)-->
								<div class="d-flex justify-content-start mb-10" v-if="message.receiveble_id == user.id">
									<!--begin::Wrapper-->
									<div class="d-flex flex-column align-items-start">
										<!--begin::User-->
										<div class="d-flex align-items-center mb-2">
											<!--begin::Avatar-->
											<div class="symbol symbol-35px symbol-circle">
												<!-- <img alt="Pic" :src="'assets/media/avatars/300-25.jpg'" /> -->
												<avatar :fullname="message.name??'Anonymous'" size="40"></avatar>
											</div>
											<!--end::Avatar-->
											<!--begin::Details-->
											<div class="ms-3">
												<a href="javascript:void(0)"
													class="fs-5 fw-bold text-gray-900 text-hover-primary me-1">{{ message.name??'Anonymous' }}</a>
												<span class="text-muted fs-7 mb-1">{{ timeFormat(message.created_at) }}</span>
											</div>
											<!--end::Details-->
										</div>
										<!--end::User-->
										<!--begin::Text-->
										<div class="p-5 rounded bg-light-info text-dark fw-semibold mw-lg-400px text-start"
											data-kt-element="message-text">{{ message.message }}</div>
										<!--end::Text-->
									</div>
									<!--end::Wrapper-->
								</div>
								<!--end::Message(in)-->
								<!--begin::Message(out)-->
								<div class="d-flex justify-content-end mb-10" v-else-if="message.sendable_id == user.id">
									<!--begin::Wrapper-->
									<div class="d-flex flex-column align-items-end">
										<!--begin::User-->
										<div class="d-flex align-items-center mb-2">
											<!--begin::Details-->
											<div class="me-3">
												<span class="text-muted fs-7 mb-1">{{ timeFormat(message.created_at) }}</span>
												<a href="javascript:void(0)"
													class="fs-5 fw-bold text-gray-900 text-hover-primary ms-1">{{ translates.You }}</a>
											</div>
											<!--end::Details-->
											<!--begin::Avatar-->
											<div class="symbol symbol-35px symbol-circle">
												<!-- <img class="img-fluid" alt="Pic" :src="'assets/media/avatars/300-1.jpg'" /> -->
												<avatar :fullname="message.name??'Anonymous'" size="40"></avatar>
											</div>
											<!--end::Avatar-->
										</div>
										<!--end::User-->
										<!--begin::Text-->
										<div class="p-5 rounded bg-light-primary text-dark fw-semibold mw-lg-400px text-end"
											data-kt-element="message-text">{{ message.message }}</div>
										<!--end::Text-->
									</div>
									<!--end::Wrapper-->
								</div>
								<!--end::Message(out)-->
							</div>
						</div>
						<div v-else class="no_message_div">
							<p class="text-muted">{{ translates.No_messages_found }}</p>
						</div>
					</div>
					<!--end::Messages-->
				</div>
				<!--end::Card body-->
				<!--begin::Card footer-->
				<form class="position-relative" @submit.prevent="sendMessage">
					<div class="position-absolute bottom-0 end-0 bg-white w-100 z-index-1 card-footer pt-4" id="kt_drawer_chat_messenger_footer">
						<!--begin::Input-->
						<textarea class="form-control form-control-flush mb-3" rows="1" v-model="send_message.message" @keydown.enter.prevent="sendMessage"
						:placeholder="translates.Type_a_message"></textarea>
						<!--end::Input-->
						<div class="text">
							<span class="text-danger">{{ sendMessageError.message }}</span>
						</div>
						<!--begin:Toolbar-->
						<div class="d-flex flex-stack">
							<!--begin::Actions-->
							<div class="d-flex align-items-center me-2">

							</div>
							<!--end::Actions-->
							<!--begin::Send-->
							<button class="btn btn-primary" type="submit">{{translates.Send}}</button>
							<!--end::Send-->
						</div>
						<!--end::Toolbar-->
					</div>
				</form>

				<!--end::Card footer-->
			</div>
			<!--end::Messenger-->
		</div>
	</div>

	<Menu :menus="menus" :event="event" @showLoginForm="showLoginForm" :user="user" :langdata="langdata" :translates="translates"></Menu>
	<div class="container">
		<!--begin::Row-->
		<div class="row mt-5">
		<!-- <code>{{ event.header_image_height }}</code> -->
			<div class="w-100 d-flex justify-content-center" v-if="event.header_image_path">
				<img class="video-image"
					:src="event.header_image_path"
					:style="{'max-width': `${event.header_image_width>0?event.header_image_width+'px':'100%'}`,
					'height':`${event.header_image_height>0? event.header_image_height+'px' : 'auto'}`}"/>
			</div>
		</div>
		<div class="row g-5 g-xl-8 mb_100" v-if="event.layout_type == '2' && checkLogin() && showSections">
			<div class="col-sm-12 col-xl-6 d-flex align-items-center">
				<!--begin::Header-->
				<div class="border-0 pt-5 align-items-center">
					<h3 class="d-flex align-items-start flex-column">
						<div v-if="event.title_style" v-html="event.title_style"></div>
						<span v-else class="fw-bold text-dark fs-1">{{ event.title }}</span>
						<span class=" fw-semibold fs-5 mt-5 mb-5" v-html="event.subtitle"></span>
						<span v-if="event.show_start_time" class=" fs-4 fw-semibold mb-5">{{ showStartTime(event.start_time) }}</span>

						<vue-countdown :time="time" v-slot="{ days, hours, minutes, seconds }" v-if="event.show_countdown && time > 0">
						<p class="fs-4 fw-semibold">{{ translates.Starts_in }} {{ days }}d {{ hours }}h {{ minutes }}m {{ seconds }}s</p>
						</vue-countdown>

					</h3>
				</div>
				<!--end::Header-->
			</div>
			<div class="col-sm-12 col-xl-6 d-flex justify-content-end align-items-center">
				<div class="mb-5" v-if="event.logo_path">
					<div class="">
						<!-- <h1 class="text-center">Company Logo</h1> -->
						<div class="d-flex justify-content-end">
							<img :src="event.logo_path"
								alt="logo" class="img-fluid logo-width-height  mt-5"
								:style="{'width': `${event.logo_image_width? event.logo_image_width+'px':'auto'}`,
										'height':`${event.logo_image_height? event.logo_image_height+'px' : 'auto'}`}"/>
						</div>
					</div>
				</div>
			</div>
			<div class="col-sm-12 col-md-12 col-lg-7 mt-0 pt-0" :style="{'width': `${votingSidebarWidthOpposide(event.voting_sidebar_width)}`+'%'}">
				<!--begin::List Widget 1-->
				<div class="">
					<!--begin::Body-->
					<div class="">
						<!--begin::Video-->
						<div class="mb-3 w-100">
							<div class="w-100" v-if="false">
								<iframe width="100%" height="390" class="rounded"
								src="https://www.youtube.com/embed/GGo3MVBFr1A?autoplay=0&mute=1" title="YouTube video player"
								frameborder="0"
								allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
								allowfullscreen></iframe>
							</div>

							<div class="w-100 d-flex jusify-content-center align-item-center" v-if="event.login_image_path"
							:style="{'max-width': `${event.login_image_width? event.login_image_width+'px':'100%'}`,
							'height': `${event.login_image_height >0? event.login_image_height+'px':'auto'}`}"
							>
								<img class="video-image rounded" :src="event.login_image_path" alt="">
							</div>
						</div>
						<!--end::Video-->
						<div class="display_under_message d-none">
							<p>{{ messageUnderDisplay }}</p>
						</div>
						<div class="mt-5 mb-5">
							<h3></h3>
							<p class="event_des" v-html="event.description"></p>
						</div>
					</div>
					<!--end::Body-->
				</div>
				<!--end::List Widget 1-->
			</div>


			<div class="col-sm-12 col-md-12 col-lg-5 mt-0 pt-0" :style="{'width': `${votingSidebarWidth(event.voting_sidebar_width)}`+'%'}">
				<!--begin::List Widget 2-->
				<div class="card mt-0 mb-5 w-100">
					<!--begin::Accordion-->
					<div class="accordion bg-white rounded" id="sidebar_voting_accordian" v-if="event.allow_poll && user">
						<div class="accordion-item rounded">
							<h2 class="rounded accordion-header bg-white" id="sidebar_voting_accordian_header">
								<button class="border-bottom bg-white accordion-button fs-4 fw-semibold" :class="[collapsed?'collapsed':'']" @click="collapseVoting"
									type="button" data-bs-target="#sidebar_voting_accordian_body"
									:aria-expanded="!collapsed" aria-controls="sidebar_voting_accordian_body">
									<h3 class="card-title fw-bold text-dark">{{ translates.Voting }} </h3>
								</button>
							</h2>


							<div id="sidebar_voting_accordian_body" class="accordion-collapse collapse" :class="[!collapsed?'show':'']"
								aria-labelledby="sidebar_voting_accordian_header" data-bs-parent="#sidebar_voting_accordian" :style="{'height':`${event.voting_sidebar_height?event.voting_sidebar_height+'px':'560px'}`, 'overflow': 'auto'}">
								<div class="accordion-body rounded">
									<div class="card-body pt-2 p_10 overflow-scroll">
										<div v-if="checkLogin() && Object.keys(sidebarVotings).length">
											<div v-for="(voting, index) in sidebarVoteGetter" :key="index">
												<div class="mt_30 voting_single" v-if="voting.showVote.title || voting.showVote.vote || voting.showVote.answer">

													<div>
														<div v-if="voting.showVote.title">
															<h1 class="text-center">{{ voting.title }}</h1>
														</div>

														<div v-if="voting.showVote.vote">

															<div class="d-flex justify-content-center align-centrer flex-row mt-3 mb-3" v-if="voting.voting_type == 'AGM'">

																<div class="" v-if="voting.info.agm_type == 1">
																	<!-- <button type="button" class="btn btn-success me-3" @click="sendAGMVote('yes', voting)">{{ translates.Yes }}</button> -->
																	<button type="button" class="btn btn-success me-2 mb-2" @click="sendAGMVote('yes', voting)">{{ voting.info.input_fields.Yes[langdata.locale]??translates.Yes }}</button>
																	<button type="button" class="btn btn-danger me-2 mb-2" @click="sendAGMVote('no', voting)">{{ voting.info.input_fields.No[langdata.locale]??translates.No }}</button>
																	<button type="button" class="btn btn-primary mb-2" @click="sendAGMVote('abstaining', voting)">{{ voting.info.input_fields.Abstaining[langdata.locale]??translates.Abstaining }}</button>
																</div>


																<div v-if="voting.info.agm_type == 2">
																	<button type="button" :class="voting.voting_result.Mover?'btn me-2 mb-2 btn-secondary':'btn me-2 mb-2 btn-primary'" v-if="voting.info.mover == 1 && !voting.voting_result.Mover && !voting.voting_result.Secon || voting.info.mover == 1 && voting.voting_result.Mover && !voting.voting_result.Seconder" @click="sendAGMVote('mover', voting)" :disabled="voting.voting_result.Mover">{{ translates.Mover }}</button>
																	<button type="button" :class="(voting.voting_result.Seconder || !voting.voting_result.Mover)?'btn btn-secondary mb-2':'btn btn-danger mb-2'" v-if="voting.info.secondary == 1 && !voting.voting_result.Mover && !voting.voting_result.Secon || voting.info.secondary == 1 && voting.voting_result.Mover && !voting.voting_result.Seconder" @click="sendAGMVote('seconder', voting)" :disabled="voting.voting_result.Seconder || !voting.voting_result.Mover">{{ translates.Seconder }}</button>
																</div>
															</div>

															<div class="mt_30" v-else-if="voting.voting_type == 'Poll' || voting.voting_type == 'Survey'">
																<div class="input-group mb-3 " v-for="(input, index) in votingInputs[voting.id]" :key="index">
																	<div class="d-flex flex-column w-100" v-if="input.type == 'text'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<input type="text" class="bg-light w-100 form-control" :placeholder="input.placeholder" v-model="votingForm[voting.id][input.name]">
																	</div>
																	<div class="d-flex flex-column w-100" v-if="input.type == 'number'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<input type="number" class="bg-light w-100 form-control" :placeholder="input.placeholder" v-model="votingForm[voting.id][input.name]">
																	</div>

																	<div class="d-flex flex-column w-100" v-if="input.type == 'textarea'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<textarea class="bg-light form-control" aria-label="With textarea" :placeholder="input.placeholder" v-model="votingForm[voting.id][input.name]"></textarea>
																	</div>

																	<div class="d-flex flex-column w-100" v-if="input.type == 'radio-group'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${input.label}`"></i>
																		</label>
																		<div class="d-flex flex-row w-100 flex-wrap">
																			<div class="form-check form-check-custom form-check-solid me-10 pb_10 w-100" v-for="(option, i) in input.values" :key="i">
																				<input class="form-check-input h-20px w-20px" type="radio" v-model="votingForm[voting.id][input.name]"
																					:value="option.value" :id="option.value.split(' ').join('_')" />
																				<label class="form-check-label">
																					{{ option.label }}
																				</label>
																			</div>
																		</div>
																	</div>

																	<div class="d-flex flex-column w-100" v-if="input.type == 'checkbox-group'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<div class="d-flex flex-row w-100 flex-wrap">
																			<div class="form-check form-check-custom form-check-solid me-10 pb_10 w-100" v-for="(option, i) in input.values" :key="i">
																				<input class="form-check-input h-20px w-20px" type="checkbox" v-model="votingForm[voting.id][input.name][option.label]" @change="handleCheckboxChange(voting.info, votingForm[voting.id][input.name], option)"
																				:value="option.value" :id="'checkbox_'+ option.value.split(' ').join('_')" />
																				<label class="form-check-label">
																					{{ option.label }}
																				</label>
																			</div>
																		</div>

																	</div>
																	<div class="d-flex flex-column w-100" v-if="input.type == 'select'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<div class="d-flex flex-row w-100">
																			<select v-model="votingForm[voting.id][input.name]" :multiple="input.multiple"
																				class="form-select" :data-placeholder="input.placeholder??'Select One'">
																				<option :value="option.value" v-for="(option, i) in input.values" :selected="option.selected" :key="i">{{ option.label }}</option>
																			</select>
																		</div>

																	</div>

																	<div class="d-flex flex-column w-100" v-if="input.type == 'file'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<div class="d-flex flex-row w-100">
																			<input type="file" class="w-100 form-control"
																				aria-describedby="inputGroupFileAddon03" :aria-label="input.placeholder??'Upload'">
																		</div>

																	</div>

																	<div class="d-flex flex-column w-100" v-if="input.type == 'date'">
																		<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																			<span :class="{required : input.required}" v-html="input.label"></span>
																			<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																			:title="`${getContentFromDiv(input.label)}`"></i>
																		</label>
																		<!--begin::Input-->
																		<div class="position-relative align-items-center">
																			<!--begin::Icon-->
																			<!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
																			<span class="svg-icon svg-icon-2 position-absolute mx-4 mt_12">
																				<svg width="24" height="24" viewBox="0 0 24 24"
																					fill="none" xmlns="http://www.w3.org/2000/svg">
																					<path opacity="0.3"
																						d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
																						fill="currentColor" />
																					<path
																						d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
																						fill="currentColor" />
																					<path
																						d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
																						fill="currentColor" />
																				</svg>
																			</span>
																			<!--end::Svg Icon-->
																			<!--end::Icon-->
																			<!--begin::Datepicker-->
																			<input name="start_time"
																				class="form-control form-control-solid ps-12 custom_date"
																				:placeholder="input.placeholder??'Select a date'" v-model="votingForm[voting.id][input.name]" type="date" />
																			<!--end::Datepicker-->
																		</div>
																		<!--end::Input-->
																	</div>
																	<div class="text" v-if="votingFormError[voting.id]">
																		<span class="text-danger">{{ votingFormError[voting.id][input.name] }}</span>
																	</div>

																	<!-- for voting form paragraph-->
																	<div v-if="input.type == 'paragraph'" v-html="input.label"></div>

																</div>
																<button type="button" class="mt-5 btn btn-primary" @click="pollSubmit(voting)">{{ translates.Submit }}</button>
															</div>


														</div>

													</div>
													<div class="voting mt_20" v-if="voting.showVote.answer && votingResult[voting.id]">
														<h2 class="text-center">{{ translates.Voting_Result }}</h2>
														<ul class="list-group">
															<li class="list-group-item d-flex justify-content-between">
																<p class="form-label">{{ translates.Total_Vote }} </p>
																<p class="form-label">{{ votingResult[voting.id].total_vote }}</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
																<p class="form-label">{{ voting.info.input_fields.Yes[langdata.locale]??translates.Yes }} </p>
																<p class="form-label">{{ votingResult[voting.id].Yes }}%</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
																<p class="form-label">{{ voting.info.input_fields.No[langdata.locale]??translates.No }} </p>
																<p class="form-label">{{ votingResult[voting.id].No }}%</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
																<p class="form-label">{{ voting.info.input_fields.Abstaining[langdata.locale]??translates.Abstaining }} </p>
																<p class="form-label">{{ votingResult[voting.id].Abstaining }}%</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 2">
																<p class="form-label">{{ translates.Mover }} </p>
																<p class="form-label">{{ votingResult[voting.id].mover_voter_name }}</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 2">
																<p class="form-label">{{ translates.Seconder }} </p>
																<p class="form-label">{{ votingResult[voting.id].seconder_voter_name }}</p>
															</li>

															<div  v-if="voting.voting_type =='Poll' || voting.voting_type =='Survey'">
																<li class="list-group-item d-flex justify-content-between" v-for="(r_field, c_i) in votingResult[voting.id]['custom_voting_data']" :key="c_i">
																	<!-- {{ r_field }} -->
																	<div class="row w-100">
																		<div class="col-lg-5">
																			<p class="form-label" v-html="r_field.label"></p>
																		</div>
																		<div class="col-lg-7">
																			<p class="d-flex justify-content-between" v-for="(value, t_i) in r_field.values" :key="t_i">
																				<strong>{{t_i}} : </strong> <span class="text-nowrap">{{ value }} %</span>
																			</p>
																		</div>
																	</div>
																</li>
															</div>
														</ul>
													</div>


												</div>

											</div>
										</div>
										<div class="card mb-5" v-else>
											<div class="card-body">
												<div class="mt-5">
													<h4 class="text-center text-muted" v-if="givenVoteID.length">Your vote was received.</h4>
													<h4 class="text-center text-muted" v-else-if="notGivenVoteID.length && user.voter == 'Yes'">Voting is Closed.</h4>
													<h4 class="text-center text-muted" v-else>{{ translates.Voting_hasnot_started_yet }}</h4>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>

					</div>
					<!--end::Accordion-->
				</div>
				<!--end::List Widget 2-->

				<!--begin::List Widget 2-->
				<div class="card" v-if="event.allow_ans_msg">
					<!--begin::Header-->
					<div class="card-header border-0 p-4">
						<h3 class="card-title fw-bold text-dark">{{ translates.Answers }}</h3>
					</div>
					<!--end::Header-->
					<!--begin::Body-->
					<div class="card-body pt-2 p-4">
						<div class="h-300px overflow-scroll" id="answer_list">
							<div class="table-responsive w-100" v-if="answers.length">
								<!--begin::Table-->
								<table class="table align-middle gs-0 gy-5">
									<!--begin::Table head-->
									<thead>
										<tr>
											<th class="p-0 w-50px"></th>
											<th class="p-0 min-w-400px"></th>
										</tr>
									</thead>
									<!--end::Table head-->
									<!--begin::Table body-->
									<tbody>
										<tr v-for="answer in answers" :key="answer.id">
											<td>
												<div class="symbol symbol-50px me-2">
													<span class="symbol-label" style="margin-top:-50px">
														<avatar v-if="answer.is_anonymous || !answer.author" :fullname="translates.Anonymous" size="40"></avatar>
														<avatar v-else :fullname="answer.author.first_name" size="40"></avatar>
													</span>
												</div>
											</td>
											<td>
												<div>
													<p class="text-dark fw-bold text-hover-primary mb-1 fs-6 cursor-pointer" v-if="answer.is_anonymous || !answer.author">
														{{ translates.Anonymous }}
													</p>
													<p class="text-dark fw-bold text-hover-primary mb-1 fs-6 cursor-pointer" v-else>
														{{ answer.author.first_name }} {{ answer.author.last_name }}
													</p>
												</div>
												<div>
													<span class="badge badge-light-danger fw-bold question text-normal text-left">
														{{ answer.question }}
													</span>

													<p class="fw-bold question_ans ml_10">
														{{ answer.reply }}
													</p>
												</div>
											</td>

										</tr>
									</tbody>
									<!--end::Table body-->
								</table>
								<!--end::Table-->
							</div>

							<div class="mb-5 d-flex justify-content-center align-items-center min-h-200px" v-else>
								<p class="fs-1 fw-bold">{{ translates.No_answer_found }}</p>
							</div>
						</div>
					</div>
					<!--end::Body-->
				</div>
				<!--end::List Widget 2-->

				<div class="card mt-5 mb-5" v-if="event.allow_qa">
					<div class="card-body p-4">
						<h3 class="">{{ translates.Ask_Question }}</h3>
						<form class=" mb-4 align-items-center" @submit.prevent="askQuestion">
							<!--begin::Input group-->
							<div class="position-relative w-md-100 me-md-2 d-flex">
								<input type="text" class="form-control mt-3 form-control-solid ps-10"
									name="search" v-model="send_question.question" :placeholder="translates.Type_Here" />
								<!--begin:Action-->
								<div class="d-flex align-items-center">
									<button type="submit" class="btn btn-primary mt-3 me-5">
										{{translates.Send}}
									</button>
								</div>
								<!--end:Action-->
							</div>
							<!--end::Input group-->

							<div class="text">
								<span class="text-danger">{{ questionError }}</span>
							</div>
							<div class="d-flex mt_10" v-if="event.allow_is_anonymous && user">
								<div>
									<span class="me-1 fs-5 fw-normal">{{ translates.Ask_anonymously }}</span>
								</div>

								<!--begin::Action-->
								<div
									class="form-check form-switch form-check-custom form-check-solid">
									<input class="form-check-input ms-5 h-20px w-30px"
										type="checkbox" value="1" v-model="send_question.is_anonymous"/>
								</div>
								<!--end::Action-->
							</div>
						</form>
					</div>
				</div>

			</div>
		</div>


		<div v-if="event.layout_type == '1' && checkLogin() && showSections">
			<div class="d-flex justify-content-center align-center">
				<!--begin::List Widget 1-->
				<div class="mb-5 align-items-center w-100">
					<div class="mt-5 mb-5" v-if="event.logo_path">
						<div class="text-center d-flex justify-content-center align-items-center m-0 m-auto w-100">
							<!-- <h1 class="align-items-center text-center">Company Logo</h1> -->
							<div class="d-flex justify-content-center w-100">
								<img :src="event.logo_path"
									alt="logo" class="img-fluid"
									:style="{'width': `${event.logo_image_width? event.logo_image_width+'px':'auto'}`,
										'height':`${event.logo_image_height? event.logo_image_height+'px' : 'auto'}`}"
								/>
							</div>
						</div>
					</div>
					<!--begin::Header-->
					<div class="align-items-center border-0">
						<div class="d-flex align-items-center flex-column">
							<!-- <h1 class="text-center fw-bold text-dark fs-1">{{ event.title }}</h1> -->
							<div v-if="event.title_style" v-html="event.title_style"></div>
							<h1 v-else class="text-center fw-bold text-dark fs-1">{{ event.title }}</h1>
							<span class="text-center mt-1 fw-semibold fs-5 mt-5 mb-5" v-html="event.subtitle"></span>
							<!-- <span class="text-center text-muted mt-1 mb-5 fw-semibold fs-5">Sunday 25 December 2025, 1:00PM
								EST</span> -->
							<span v-if="event.show_start_time" class="text-center mt-1 mb-5 fw-semibold fs-5">{{ showStartTime(event.start_time) }}</span>

							<vue-countdown :time="time" v-slot="{ days, hours, minutes, seconds }" v-if="event.show_countdown && time > 0">
							<p class="fs-4 fw-semibold">{{translates.Starts_in}} {{ days }}d {{ hours }}h {{ minutes }}m {{ seconds }}s</p>
							</vue-countdown>

						</div>

					</div>
					<!--end::Header-->
					<!--begin::Body-->
					<div class="align-items-center pt-5 w-100 ">
						<!--begin::Video-->
						<div class="mb-3 w-100">

									<div class="w-100 mt-5  ">
										<div class="w-100" v-if="false">
											<iframe width="100%" height="390" class="rounded"
											src="https://www.youtube.com/embed/GGo3MVBFr1A" title="YouTube video player"
											frameborder="0"
											allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
											allowfullscreen></iframe>
										</div>
										<div class="w-100 d-flex justify-content-center">
											<div class="d-flex justify-content-center w-100" v-if="event.login_image_path"
												:style="{'max-width': `${event.login_image_width? event.login_image_width+'px':'auto'}`,
												'height': `${event.login_image_height>0? event.login_image_height+'px' : 'auto'}`}"
											>
												<img class="video-image" :src="event.login_image_path" alt="">
											</div>
										</div>

									</div>
									<div class="display_under_message">
										<p>{{ messageUnderDisplay }}</p>
									</div>

									<div class="mt-5 align-items-center mb-5">
										<h3 class="text-center"></h3>
										<p class="text-center w-100 event_des" v-html="event.description"></p>
									</div>


						</div>
						<!--end::Video-->
					</div>
					<!--end::Body-->
				</div>
				<!--end::List Widget 1-->
			</div>
			<!--begin::Row-->
			<div class="row g-5 g-xl-8 mb_100">

				<div class="col-sm-12 col-xl-6" v-if="event.allow_ans_msg" :style="{'width': `${votingSidebarWidthOpposide(event.voting_sidebar_width)}`+'%'}">
					<!--begin::List Widget 1-->
					<div class="card">
						<h3 class="card-title fw-bold ms-5 mt-5 pt-3 ps-5 text-dark">{{translates.Answers}}</h3>

						<!--begin::Body-->
						<div class="card-body pt-5">
							<!--begin::Body-->
							<div class="pt-2 h-300px overflow-scroll" id="answer_list">
								<div class="table-responsive w-100"  v-if="answers.length">
									<!--begin::Table-->
									<table class="table align-middle gs-0 gy-5">
										<!--begin::Table head-->
										<thead>
											<tr>
												<th class="p-0 w-50px"></th>
												<th class="p-0 min-w-400px"></th>
											</tr>
										</thead>
										<!--end::Table head-->
										<!--begin::Table body-->
										<tbody>
											<tr v-for="answer in answers" :key="answer.id">
												<td>
													<div class="symbol symbol-50px me-2">
														<span class="symbol-label" style="margin-top:-50px">
															<avatar v-if="answer.is_anonymous || !answer.author" :fullname="translates.Anonymous" size="40"></avatar>
															<avatar v-else :fullname="answer.author.first_name" size="40"></avatar>
														</span>
													</div>
												</td>
												<td>
													<div>
														<p class="text-dark fw-bold text-hover-primary mb-1 fs-6 cursor-pointer" v-if="answer.is_anonymous || !answer.author">
															{{ translates.Anonymous }}
														</p>
														<p class="text-dark fw-bold text-hover-primary mb-1 fs-6 cursor-pointer" v-else>
															{{ answer.author.first_name }}
														</p>
													</div>
													<div>
														<span class="badge badge-light-danger fw-bold question text-normal text-left">
															{{ answer.question }}
														</span>

														<p class="fw-bold question_ans ml_10">
															{{ answer.reply }}
														</p>
													</div>
												</td>

											</tr>
										</tbody>
										<!--end::Table body-->
									</table>
									<!--end::Table-->
								</div>
								<div class="mb-5 d-flex justify-content-center align-items-center min-h-200px" v-else>
									<p class="fs-1 fw-bold">{{ translates.No_answer_found }}</p>
								</div>
							</div>
							<!--end::Body-->
						</div>
						<!--end::Body-->
					</div>
					<!--end::List Widget 1-->

					<div class="card mt-5 mb-5" v-if="event.allow_qa">
						<div class="card-body">
							<h3 class="">{{translates.Ask_Question}}</h3>
							<form class=" mb-4 align-items-center" @submit.prevent="askQuestion">
								<!--begin::Input group-->
								<div class="position-relative w-md-100 me-md-2 d-flex">
									<!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->

									<!--end::Svg Icon-->
									<input type="text" class="form-control mt-3 form-control-solid ps-10"
										name="search" v-model="send_question.question" :placeholder="translates.Type_Here" />

									<!--begin:Action-->
									<div class="d-flex align-items-center">
										<button type="submit" class="btn btn-primary mt-3 me-5">
											{{translates.Send}}
										</button>
									</div>
									<!--end:Action-->
								</div>
								<!--end::Input group-->

								<div class="text">
									<span class="text-danger">{{ questionError }}</span>
								</div>

								<div class="d-flex mt_10" v-if="event.allow_is_anonymous && user">
									<div>
										<span class="me-1 fs-5 fw-normal">{{ translates.Ask_anonymously }}</span>
									</div>

									<!--begin::Action-->
									<div
										class="form-check form-switch form-check-custom form-check-solid">
										<input class="form-check-input ms-5 h-20px w-30px"
											type="checkbox" value="1" v-model="send_question.is_anonymous"/>
									</div>
									<!--end::Action-->
								</div>
							</form>
						</div>
					</div>
				</div>

				<div class="col-sm-12 col-xl-6" :style="{'width': `${votingSidebarWidth(event.voting_sidebar_width)}`+'%'}">
					<!--begin::List Widget 2-->

					<div class="card mt-0 mb-5">

						<!--begin::Accordion-->
						<div class="accordion bg-white rounded" id="sidebar_voting_accordian" v-if="event.allow_poll && user">
							<div class="accordion-item rounded">
								<h2 class="rounded accordion-header bg-white" id="sidebar_voting_accordian_header">
									<button class="border-bottom bg-white accordion-button fs-4 fw-semibold" :class="[collapsed?'collapsed':'']" @click="collapseVoting"
										type="button" data-bs-target="#sidebar_voting_accordian_body"
										:aria-expanded="!collapsed" aria-controls="sidebar_voting_accordian_body">
										<h3 class="card-title fw-bold text-dark">{{ translates.Voting }} </h3>
									</button>
								</h2>
								<div id="sidebar_voting_accordian_body" class="accordion-collapse collapse" :class="[!collapsed?'show':'']"
									aria-labelledby="sidebar_voting_accordian_header" data-bs-parent="#sidebar_voting_accordian" :style="{'height':`${event.voting_sidebar_height?event.voting_sidebar_height+'px':'560px'}`, 'overflow': 'auto'}">
									<div class="accordion-body rounded">
										<div class="card-body pt-2 p_10 overflow-scroll">
											<div v-if="checkLogin() && Object.keys(sidebarVotings).length">
												<div v-for="(voting, index) in sidebarVoteGetter" :key="index" class="mt_30 voting_single">
													<div v-if="voting.showVote.title || voting.showVote.vote || voting.showVote.answer">

														<div v-if="voting.showVote.title">
															<h1 class="text-center">{{ voting.title }}</h1>
														</div>
														<div class="pb-5" v-if="voting.showVote.vote">

															<div class="d-flex justify-content-center align-centrer flex-row mt-3 mb-3" v-if="voting.voting_type == 'AGM'">

																<div class="" v-if="voting.info.agm_type == 1">
																	<button type="button" class="btn btn-success me-2 mb-2" @click="sendAGMVote('yes', voting)">{{ voting.info.input_fields.Yes[langdata.locale]??translates.Yes }}</button>
																	<button type="button" class="btn btn-danger me-2 mb-2" @click="sendAGMVote('no', voting)">{{ voting.info.input_fields.No[langdata.locale]??translates.No }}</button>
																	<button type="button" class="btn btn-primary mb-2" @click="sendAGMVote('abstaining', voting)">{{ voting.info.input_fields.Abstaining[langdata.locale]??translates.Abstaining }}</button>
																</div>


																<div v-if="voting.info.agm_type == 2">
																	<button type="button" :class="voting.voting_result.Mover?'btn me-2 mb-2 btn-secondary':'btn me-2 mb-2 btn-primary'" v-if="voting.info.mover == 1 && !voting.voting_result.Mover && !voting.voting_result.Secon || voting.info.mover == 1 && voting.voting_result.Mover && !voting.voting_result.Seconder" @click="sendAGMVote('mover', voting)" :disabled="voting.voting_result.Mover">{{ translates.Mover }}</button>
																	<button type="button" :class="(voting.voting_result.Seconder || !voting.voting_result.Mover)?'btn btn-secondary mb-2':'btn btn-danger mb-2'" v-if="voting.info.secondary == 1 && !voting.voting_result.Mover && !voting.voting_result.Secon || voting.info.secondary == 1 && voting.voting_result.Mover && !voting.voting_result.Seconder" @click="sendAGMVote('seconder', voting)" :disabled="voting.voting_result.Seconder || !voting.voting_result.Mover">{{ translates.Seconder }}</button>
																</div>
															</div>

															<div class="mt_30" v-else-if="voting.voting_type == 'Poll' || voting.voting_type == 'Survey'">
															<div class="input-group mb-3 " v-for="(input, index) in votingInputs[voting.id]" :key="index">
																<div class="d-flex flex-column w-100" v-if="input.type == 'text'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<input type="text" class="bg-light w-100 form-control" :placeholder="input.placeholder" v-model="votingForm[voting.id][input.name]">
																</div>
																<div class="d-flex flex-column w-100" v-if="input.type == 'number'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<input type="number" class="bg-light w-100 form-control" :placeholder="input.placeholder" v-model="votingForm[voting.id][input.name]">
																</div>

																<div class="d-flex flex-column w-100" v-if="input.type == 'textarea'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<textarea class="bg-light form-control" aria-label="With textarea" :placeholder="input.placeholder" v-model="votingForm[voting.id][input.name]"></textarea>
																</div>

																<div class="d-flex flex-column w-100" v-if="input.type == 'radio-group'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<div class="d-flex flex-row w-100 flex-wrap">
																		<div class="form-check form-check-custom form-check-solid me-10 pb_10 w-100" v-for="(option, i) in input.values" :key="i">
																			<input class="form-check-input h-20px w-20px" type="radio" v-model="votingForm[voting.id][input.name]"
																				:value="option.value" :id="option.value.split(' ').join('_')" />
																			<label class="form-check-label">
																				{{ option.label }}
																			</label>
																		</div>
																	</div>
																</div>

																<div class="d-flex flex-column w-100" v-if="input.type == 'checkbox-group'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<div class="d-flex flex-row w-100 flex-wrap">
																		<div class="form-check form-check-custom form-check-solid me-10 pb_10 w-100" v-for="(option, i) in input.values" :key="i">
																			<input class="form-check-input h-20px w-20px" type="checkbox" v-model="votingForm[voting.id][input.name][option.label]" @change="handleCheckboxChange(voting.info, votingForm[voting.id][input.name], option)"
																			:value="option.value" :id="'checkbox_'+ option.value.split(' ').join('_')" />
																			<label class="form-check-label">
																				{{ option.label }}
																			</label>
																		</div>
																	</div>

																</div>
																<div class="d-flex flex-column w-100" v-if="input.type == 'select'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<div class="d-flex flex-row w-100">
																		<select v-model="votingForm[voting.id][input.name]" :multiple="input.multiple"
																			class="form-select" :data-placeholder="input.placeholder??'Select One'">
																			<option :value="option.value" v-for="(option, i) in input.values" :selected="option.selected" :key="i">{{ option.label }}</option>
																		</select>
																	</div>

																</div>

																<div class="d-flex flex-column w-100" v-if="input.type == 'file'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<div class="d-flex flex-row w-100">
																		<input type="file" class="w-100 form-control"
																			aria-describedby="inputGroupFileAddon03" :aria-label="input.placeholder??'Upload'">
																	</div>

																</div>

																<div class="d-flex flex-column w-100" v-if="input.type == 'date'">
																	<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
																		<span :class="{required : input.required}" v-html="input.label"></span>
																		<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
																		:title="`${getContentFromDiv(input.label)}`"></i>
																	</label>
																	<!--begin::Input-->
																	<div class="position-relative align-items-center">
																		<!--begin::Icon-->
																		<!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
																		<span class="svg-icon svg-icon-2 position-absolute mx-4 mt_12">
																			<svg width="24" height="24" viewBox="0 0 24 24"
																				fill="none" xmlns="http://www.w3.org/2000/svg">
																				<path opacity="0.3"
																					d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
																					fill="currentColor" />
																				<path
																					d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
																					fill="currentColor" />
																				<path
																					d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
																					fill="currentColor" />
																			</svg>
																		</span>
																		<!--end::Svg Icon-->
																		<!--end::Icon-->
																		<!--begin::Datepicker-->
																		<input name="start_time"
																			class="form-control form-control-solid ps-12 custom_date"
																			:placeholder="input.placeholder??'Select a date'" v-model="votingForm[voting.id][input.name]" type="date" />
																		<!--end::Datepicker-->
																	</div>
																	<!--end::Input-->
																</div>

																<div class="text" v-if="votingFormError[voting.id]">
																	<span class="text-danger">{{ votingFormError[voting.id][input.name] }}</span>
																</div>

																<!-- for voting form paragraph-->
																<div v-if="input.type == 'paragraph'" v-html="input.label"></div>

															</div>
															<button type="button" class="mt-5 btn btn-primary" @click="pollSubmit(voting)">{{ translates.Submit }}</button>
															</div>

														</div>

													</div>
													<div class="voting px-5 pb-5" v-if="voting.showVote.answer && votingResult[voting.id]">
														<h2 class="text-center">{{ translates.Voting_Result }}</h2>
														<ul class="list-group">
															<li class="list-group-item d-flex justify-content-between">
																<p class="form-label">{{ translates.Total_Vote }} </p>
																<p class="form-label">{{ votingResult[voting.id].total_vote }}</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
																<p class="form-label">{{ voting.info.input_fields.Yes[langdata.locale]??translates.Yes }} </p>
																<p class="form-label">{{ votingResult[voting.id].Yes }}%</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
																<p class="form-label">{{ voting.info.input_fields.No[langdata.locale]??translates.No }} </p>
																<p class="form-label">{{ votingResult[voting.id].No }}%</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
																<p class="form-label">{{ voting.info.input_fields.Abstaining[langdata.locale]??translates.Abstaining }} </p>
																<p class="form-label">{{ votingResult[voting.id].Abstaining }}%</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 2">
																<p class="form-label">{{ translates.Mover }} </p>
																<p class="form-label">{{ votingResult[voting.id].mover_voter_name }}</p>
															</li>
															<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 2">
																<p class="form-label">{{ translates.Seconder }} </p>
																<p class="form-label">{{ votingResult[voting.id].seconder_voter_name }}</p>
															</li>
															<div  v-if="voting.voting_type =='Poll' || voting.voting_type =='Survey'">
																<li class="list-group-item d-flex justify-content-between" v-for="(r_field, c_i) in votingResult[voting.id]['custom_voting_data']" :key="c_i">
																	<div class="row w-100">
																		<div class="col-lg-5">
																			<p class="form-label" v-html="r_field.label"></p>
																		</div>
																		<div class="col-lg-7">
																			<p class="d-flex justify-content-between" v-for="(value, t_i) in r_field.values" :key="t_i">
																				<strong>{{t_i}} : </strong> <span class="text-nowrap">{{ value }} %</span>
																			</p>
																		</div>
																	</div>
																</li>
															</div>
														</ul>
													</div>
												</div>
											</div>
											<div class="card mb-5" v-else>
												<div class="card-body">
													<div class="mt-5">
														<h4 class="text-center text-muted" v-if="givenVoteID.length">Your vote was received.</h4>
														<h4 class="text-center text-muted" v-else-if="notGivenVoteID.length && user.voter == 'Yes'">Voting is Closed.</h4>
														<h4 class="text-center text-muted" v-else>{{ translates.Voting_hasnot_started_yet }}</h4>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

						</div>
						<!--end::Accordion-->
					</div>


				</div>

			</div>
			<!--end::Row-->
		</div>

		<div class="" v-if="!checkLogin() && showLogin">
			<div class="pb_100 mt_20">
				<div class="row">
					<div class="col-sm-12 col-md-6">
						<div class="mb-5">
							<div v-if="event.title_style" v-html="event.title_style"></div>
							<h1 v-else>{{ event.title }}</h1>
							<p class="fs-4 fw-semibold" v-html="event.subtitle"></p>
							<p class="fs-4 fw-semibold" v-if="event.show_start_time">{{ showStartTime(event.start_time) }}</p>


							<vue-countdown :time="time" v-slot="{ days, hours, minutes, seconds }" v-if="event.show_countdown && time > 0">
							<p class="fs-4 fw-semibold">{{ translates.Starts_in }} {{ days }}d {{ hours }}h {{ minutes }}m {{ seconds }}s</p>
							</vue-countdown>

							<!-- <p class="mt-5 pt-5 mb-5" v-html="event.description"></p> -->

							<div class="card shadow-sm mt-5">
								<div class="card-body">
									<div v-html="event.login_form.header?event.login_form.header[langdata.locale]:''" class="event_des"></div>
									<form @submit.prevent="Login()">

										<div v-for="(input, index) in loginInputs" :key="index">
											<label class="fs-6 fw-semibold form-label mt-3" v-if="input.subtype != 'password' && input.type != 'header' && input.type != 'hidden' && input.type != 'paragraph' && input.label.trim()">
												<span :class="{required : input.required}" v-if="input.name == 'email' && !event.is_membership_number || input.name == 'membership_number' && !event.is_membership_number">{{input.Email??translates.Email}}</span>
												<span :class="{required : input.required}" v-else-if="input.name == 'email' && event.is_membership_number || input.name == 'membership_number' && event.is_membership_number">{{ event.custom_label_of_membership_number }}</span>
												<span :class="{required : input.required}" v-else>{{ input.label }}</span>
												<i class="fas fa-exclamation-circle ms-1 fs-7" v-if="input.name == 'email' && !event.is_membership_number || input.name == 'membership_number' && !event.is_membership_number" data-bs-toggle="tooltip" :title="translates.Email_required"></i>
												<i class="fas fa-exclamation-circle ms-1 fs-7" v-else-if="input.name == 'email' && event.is_membership_number || input.name == 'membership_number' && event.is_membership_number" data-bs-toggle="tooltip" :title="event.custom_label_of_membership_number+' required'"></i>
												<i class="fas fa-exclamation-circle ms-1 fs-7" v-else data-bs-toggle="tooltip" :title="`${input.label}`"></i>
											</label>

											<label class="fs-6 fw-semibold form-label mt-3" v-if="event.password_only && input.subtype == 'password' && input.type != 'header' && input.type != 'hidden' && input.type != 'paragraph' && input.label.trim()">
												<span :class="{required : input.required}">{{ translates.Password }}</span>
												<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="translates.Password_required"></i>
											</label>
											<input :type="input.subtype" :placeholder="translates.email" v-if="input.type == 'text' && input.subtype != 'password' && emailCheck(input)" v-model="loginForm['email']" autocomplete="false" readonly onfocus="removeAttribute('readonly');" ontouchstart="removeAttribute('readonly');"
												class="form-control form-control-solid mb-2"/>



											<input type="text" :placeholder="event.custom_label_of_membership_number" v-else-if="input.type == 'text' && input.subtype != 'password' && memberCheck(input)" v-model="loginForm['membership_number']" autocomplete="false" readonly onfocus="removeAttribute('readonly');" ontouchstart="removeAttribute('readonly');"
												class="form-control form-control-solid mb-2"/>

											<input :type="input.subtype" :placeholder="input.placeholder??`${input.label}`" v-if="input.type == 'text' && input.subtype != 'password' && input.name != '' && !emailCheck(input) && !memberCheck(input)" v-model="loginForm[input.name]" autocomplete="false" readonly onfocus="removeAttribute('readonly');" ontouchstart="removeAttribute('readonly');"
												class="form-control form-control-solid mb-2"/>

											<input :type="input.subtype" :placeholder="translates.password" v-if="event.password_only && input.type == 'text' && input.subtype == 'password'" v-model="loginForm[input.name]" autocomplete="false" readonly onfocus="removeAttribute('readonly');" ontouchstart="removeAttribute('readonly');"
												class="form-control form-control-solid mb-2"/>

											<input type="number" class="bg-light w-100 form-control mb-2" :placeholder="input.placeholder??`Enter ${input.label}`" v-if="input.type == 'number'" v-model="loginForm[input.name]">

											<textarea class="bg-light form-control mb-2" aria-label="With textarea" v-if="input.type == 'textarea'" :placeholder="input.placeholder??`Enter ${input.label}`" v-model="loginForm[input.name]"></textarea>

											<div class="d-flex flex-row w-100 flex-wrap" v-if="input.type == 'radio-group'">
												<div class="form-check form-check-custom form-check-solid me-10 pb_10" v-for="(option, i) in input.values" :key="i">
													<input class="form-check-input h-20px w-20px" type="radio" v-model="loginForm[input.name]"
														:value="option.value" :id="option.value" />
													<label class="form-check-label">
														{{ option.label }}
													</label>
												</div>
											</div>

											<div class="d-flex flex-row w-100 flex-wrap" v-if="input.type == 'checkbox-group'">
												<div class="form-check form-check-custom form-check-solid me-10 pb_10" v-for="(option, i) in input.values" :key="i">
													<input class="form-check-input h-20px w-20px" type="checkbox" v-model="loginForm[input.name][option.label]"
													:value="option.value" :id="'checkbox_'+ option.value" />
													<label class="form-check-label">
														{{ option.label }}
													</label>
												</div>
											</div>

											<div class="d-flex flex-row w-100" v-if="input.type == 'select'">
												<select v-model="loginForm[input.name]" :id="'login_'+ input.name" :multiple="input.multiple"
													class="form-select" data-placeholder="Select One"
													>
													<option :value="option.value" v-for="(option, i) in input.values" :selected="option.selected" :key="i">{{ option.label }}</option>
												</select>
											</div>

											<div class="d-flex flex-row w-100" v-if="input.type == 'file'">
												<input type="file" class="w-100 form-control mb-2"
													aria-describedby="inputGroupFileAddon03" aria-label="Upload">
											</div>

											<div class="position-relative align-items-center" v-if="input.type == 'date'">

												<span class="svg-icon svg-icon-2 position-absolute mx-4 mt_12">
													<svg width="24" height="24" viewBox="0 0 24 24"
														fill="none" xmlns="http://www.w3.org/2000/svg">
														<path opacity="0.3"
															d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
															fill="currentColor" />
														<path
															d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
															fill="currentColor" />
														<path
															d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
															fill="currentColor" />
													</svg>
												</span>

												<input name="start_time"
													class="form-control form-control-solid ps-12 custom_date mb-2"
													:placeholder="input.placeholder" v-model="loginForm[input.name]" type="date" />

											</div>

											<!-- <div class="position-relative align-items-center mt_15 mb_10" v-if="input.type == 'paragraph'">
												<p v-html="input.label"></p>
											</div>
											<div class="position-relative align-items-center mt_5 mb_5" v-if="input.type == 'header'">
												<h1 v-if="input.subtype == 'h1'" :class="input.className">{{ input.label }}</h1>
												<h2 v-else-if="input.subtype == 'h2'" :class="input.className">{{ input.label }}</h2>
												<h1 v-else-if="input.subtype == 'h3'" :class="input.className">{{ input.label }}</h1>
											</div> -->

											<div class="text" v-if="input.type == 'text' && input.subtype != 'password' && emailCheck(input)">
												<span class="text-danger">{{ loginFormError['email'] }}</span>
											</div>
											<div class="text" v-else-if="input.type == 'text' && input.subtype != 'password' && memberCheck(input)">
												<span class="text-danger">{{ loginFormError['membership_number'] }}</span>
											</div>
											<div class="text" v-else>
												<span class="text-danger">{{ loginFormError[input.name] }}</span>
											</div>

											<div v-if="input.type == 'paragraph'" v-html="input.label">
											</div>

										</div>
										<!-- <div class="text">
												<span class="text-danger">{{ this.loginFormError.error }}</span>
										</div> -->



										<div class="d-flex justify-content-between align-items-center">
											<button type="submit" class="mt-5 btn btn-primary">{{ translates.Login }}</button>
											<a v-if="event.password_only" href="" class="link-primary" @click.prevent="showForgotPassword()">{{ translates.Forgot_Password }}</a>
										</div>
									</form>

									<div v-html="event.login_form.footer?event.login_form.footer[langdata.locale]:''" class="mt_10 event_des"></div>

								</div>
							</div>
						</div>

					</div>
					<div class="col-sm-12 col-md-6">
						<div class="d-flex justify-content-center" v-if="event.logo_path">
							<img :src="event.logo_path"
								alt="logo" class="mt-5 img-fluid w-200px h-200px" />
						</div>

						<div class="d-flex flex-column justify-content-center align-items-center">
							<button v-if="registerRequired" type="button" class="mt-5 btn btn-primary" @click="showRegisterForm()">{{ translates.Register }}</button>
							<!-- <button type="button" class="w-25 mt-5 btn btn-primary">French</button> -->
						</div>
					</div>
				</div>
			</div>
		</div>



		<div class="" v-if="!checkLogin() && showRegister && !showLogin">
			<div class="pb_100 mt_20">
				<div class="row">
					<div class="col-sm-12 col-md-6">
						<div class="mb-5">
							<div v-if="event.title_style" v-html="event.title_style"></div>
							<h1 v-else>{{ event.title }}</h1>
							<p class="fs-4 fw-semibold" v-html="event.subtitle"></p>
							<p class="fs-4 fw-semibold" v-if="event.show_start_time">{{ showStartTime(event.start_time) }}</p>

							<vue-countdown :time="time" v-slot="{ days, hours, minutes, seconds }" v-if="event.show_countdown && time > 0">
							<p class="fs-4 fw-semibold">{{ translates.Starts_in }} {{ days }}d {{ hours }}h {{ minutes }}m {{ seconds }}s</p>
							</vue-countdown>


							<div class="card shadow-sm mt-5">
								<div class="card-body">
									<div v-html="event.register_form.header?event.register_form.header[langdata.locale]:''" class="event_des"></div>
									<form @submit.prevent="register()">

										<div v-for="(input, index) in registerInputs" :key="index">
											<label class="fs-6 fw-semibold form-label mt-3" v-if="input.subtype != 'password' && input.type != 'header' && input.type != 'hidden' && input.type != 'paragraph' && input.label.trim()">
												<span :class="{required : input.required}" v-if="input.name == 'name'">{{translates.Name}}</span>
												<span :class="{required : input.required}" v-else-if="input.name == 'email'">{{translates.Email}}</span>
												<span :class="{required : input.required}" v-else-if="input.name == 'phone'">{{translates.Phone}}</span>
												<span :class="{required : input.required}" v-else>{{ input.label }}</span>
												<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" v-if="input.name == 'name'"
													:title="translates.Name_required"></i>
												<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" v-else-if="input.name == 'email'"
													:title="translates.Email_required"></i>
												<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" v-else-if="input.name == 'phone'"
													:title="`${input.label}`"></i>
												<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" v-else
													:title="`${input.label}`"></i>
											</label>
											<label class="fs-6 fw-semibold form-label mt-3" v-if="event.password_only && input.subtype == 'password' && input.type != 'header' && input.type != 'hidden' && input.type != 'paragraph' && input.label.trim()">
												<span :class="{required : input.required}">{{ translates.Password }}</span>
												<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="translates.Password_required"></i>
											</label>

											<input :type="input.subtype" :placeholder="input.placeholder??'-'" v-if="input.type == 'text' && input.subtype != 'password'" v-model="registerForm[input.name]" onfocus="removeAttribute('readonly');" ontouchstart="removeAttribute('readonly');"
												class="form-control form-control-solid mb-2"/>
											<input :type="input.subtype" :placeholder="input.placeholder??'-'" v-if="event.password_only && input.type == 'text' && input.subtype == 'password'"  v-model="registerForm[input.name]" onfocus="removeAttribute('readonly');" ontouchstart="removeAttribute('readonly');"
												class="form-control form-control-solid mb-2"/>

											<input type="number" class="bg-light w-100 form-control mb-2" :placeholder="input.placeholder??'-'" v-if="input.type == 'number'" v-model="registerForm[input.name]">

											<textarea class="bg-light form-control mb-2" aria-label="With textarea" v-if="input.type == 'textarea'" :placeholder="translates.Write_here" v-model="registerForm[input.name]"></textarea>

											<div class="d-flex flex-row w-100 flex-wrap" v-if="input.type == 'radio-group'">
												<div class="form-check form-check-custom form-check-solid me-10 pb_10" v-for="(option, i) in input.values" :key="i">
													<input class="form-check-input h-20px w-20px" type="radio" v-model="registerForm[input.name]"
														:value="option.value" :id="option.value" />
													<label class="form-check-label">
														{{ option.label }}
													</label>
												</div>
											</div>

											<div class="d-flex flex-row w-100 flex-wrap" v-if="input.type == 'checkbox-group'">
												<div class="form-check form-check-custom form-check-solid me-10 pb_10" v-for="(option, i) in input.values" :key="i">
													<input class="form-check-input h-20px w-20px" type="checkbox" v-model="registerForm[input.name][option.label]"
													:value="option.value" :id="'checkbox_'+ option.value" />
													<label class="form-check-label">
														{{ option.label }}
													</label>
												</div>
											</div>

											<div class="d-flex flex-row w-100" v-if="input.type == 'select'">
												<select v-model="registerForm[input.name]" :id="'register_'+ input.name" :multiple="input.multiple"
													class="form-select" data-placeholder="Select One">
													<option :value="option.value" v-for="(option, i) in input.values" :selected="option.selected" :key="i">{{ option.label }}</option>
												</select>
											</div>

											<div class="d-flex flex-row w-100" v-if="input.type == 'file'">
												<input type="file" class="w-100 form-control mb-2"
													aria-describedby="inputGroupFileAddon03" aria-label="Upload">
											</div>

											<div class="position-relative align-items-center" v-if="input.type == 'date'">

												<span class="svg-icon svg-icon-2 position-absolute mx-4 mt_12">
													<svg width="24" height="24" viewBox="0 0 24 24"
														fill="none" xmlns="http://www.w3.org/2000/svg">
														<path opacity="0.3"
															d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
															fill="currentColor" />
														<path
															d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
															fill="currentColor" />
														<path
															d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
															fill="currentColor" />
													</svg>
												</span>
												<input name="start_time"
													class="form-control form-control-solid ps-12 custom_date mb-2"
													placeholder="Select a date" v-model="registerForm[input.name]" type="date" />

											</div>
											<!-- <div class="position-relative align-items-center mt_15 mb_10" v-if="input.type == 'paragraph'">
												<p v-html="input.label"></p>
											</div>
											<div class="position-relative align-items-center mt_5 mb_5" v-if="input.type == 'header'">
												<h1 v-if="input.subtype == 'h1'" :class="input.className">{{ input.label }}</h1>
												<h2 v-else-if="input.subtype == 'h2'" :class="input.className">{{ input.label }}</h2>
												<h1 v-else-if="input.subtype == 'h3'" :class="input.className">{{ input.label }}</h1>
											</div> -->

											<div class="text">
												<span class="text-danger">{{ registerFormError[input.name] }}</span>
											</div>

											<div v-if="input.type == 'paragraph'" v-html="input.label">
											</div>

										</div>

										<button type="submit" class="mt-5 btn btn-primary">{{ translates.Save }}</button>
									</form>

									<div v-html="event.register_form.footer?event.register_form.footer[langdata.locale]:''" class="mt_10 event_des"></div>
								</div>
							</div>
						</div>

					</div>
					<div class="col-sm-12 col-md-6">
						<div class="d-flex justify-content-center" v-if="event.logo_path">
							<img :src="event.logo_path"
								alt="logo" class="img-fluid mt-5 w-200px h-200px" />
						</div>

						<div class="d-flex flex-column justify-content-center align-items-center">
							<button type="button" class="w-25 mt-5 btn btn-primary" @click="showLoginForm()">{{ translates.Login }}</button>
							<!-- <button type="button" class="w-25 mt-5 btn btn-primary">French</button> -->
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="" v-if="!showRegister && !showLogin && showForgot && event.password_only">
			<div class="pb_100 mt_20">
				<div class="row">
					<div class="col-sm-12 col-md-6">
						<div class="mb-5">
							<div v-if="event.title_style" v-html="event.title_style"></div>
							<h1>{{ event.title }}</h1>
							<p class="fs-4 fw-semibold" v-html="event.subtitle"></p>
							<p class="fs-4 fw-semibold" v-if="event.show_start_time">{{ showStartTime(event.start_time) }}</p>

							<vue-countdown :time="time" v-slot="{ days, hours, minutes, seconds }" v-if="event.show_countdown && time > 0">
							<p class="fs-4 fw-semibold">{{ translates.Starts_in }} {{ days }}d {{ hours }}h {{ minutes }}m {{ seconds }}s</p>
							</vue-countdown>

							<!-- <p class="mt-5 pt-5 mb-5" v-html="event.description"></p> -->

							<div class="card shadow-sm mt-5">
								<div class="card-body">
									<form @submit.prevent="sendForgotLink()">
										<p class="text-center fs-3 fw-semibold">Forgot password</p>
										<!--begin::Label-->
										<label class="fs-6 fw-semibold form-label mt-3">
											<span class="required">Email</span>
											<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
												title="Email required"></i>
										</label>
										<!--end::Label-->
										<input type="email" placeholder="Enter email" v-model="forgotForm.email" class="form-control form-control-solid mb-2"/>
										<div class="text">
											<span class="text-danger">{{ forgotFormError.error }}</span>
										</div>

										<button type="submit" class="mt-5 btn btn-primary" :disabled="forgotFormError.sendButtonStatus">{{ forgotFormError.sendButtonText }}</button>
									</form>

								</div>
							</div>
						</div>

					</div>
					<div class="col-sm-12 col-md-6">
						<div class="d-flex justify-content-center" v-if="event.logo_path">
							<img :src="event.logo_path"
								alt="logo" class="img-fluid mt-5 w-200px h-200px" />
						</div>

						<div class="d-flex flex-column justify-content-center align-items-center">
							<button type="button" class="w-25 mt-5 btn btn-primary" @click="showLoginForm()">{{ translates.Login }}</button>
							<!-- <button type="button" class="w-25 mt-5 btn btn-primary">French</button> -->
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="" v-if="!showRegister && !showLogin && !showForgot && showReset && event.password_only">
			<div class="pb_100 mt_20">
				<div class="row">
					<div class="col-sm-12 col-md-6">
						<div class="mb-5">
							<div v-if="event.title_style" v-html="event.title_style"></div>
							<h1 v-else>{{ event.title }}</h1>
							<p class="fs-4 fw-semibold" v-html="event.subtitle"></p>
							<p class="fs-4 fw-semibold" v-if="event.show_start_time">{{ showStartTime(event.start_time) }}</p>

							<vue-countdown :time="time" v-slot="{ days, hours, minutes, seconds }" v-if="event.show_countdown && time > 0">
							<p class="fs-4 fw-semibold">{{ translates.Starts_in }} {{ days }}d {{ hours }}h {{ minutes }}m {{ seconds }}s</p>
							</vue-countdown>

							<!-- <p class="mt-5 pt-5 mb-5" v-html="event.description"></p> -->

							<div class="card shadow-sm mt-5">
								<div class="card-body">
									<form @submit.prevent="resetPassword()">
										<p class="text-center fs-3 fw-semibold">Reset password</p>
										<!--begin::Label-->
										<label class="fs-6 fw-semibold form-label mt-3">
											<span class="required">Email</span>
											<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
												title="Email required"></i>
										</label>
										<!--end::Label-->
										<input type="email" placeholder="Enter email" v-model="resetForm.email" class="form-control form-control-solid mb-2"/>
										<div class="text">
											<span class="text-danger">{{ resetFormError.email }}</span>
										</div>

										<!--begin::Label-->
										<label class="fs-6 fw-semibold form-label mt-3">
											<span class="required">New Password</span>
											<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
												title="New password required"></i>
										</label>
										<!--end::Label-->
										<input type="password" placeholder="Enter new password" v-model="resetForm.password" class="form-control form-control-solid mb-2"/>
										<div class="text">
											<span class="text-danger">{{ resetFormError.password }}</span>
										</div>

										<!--begin::Label-->
										<label class="fs-6 fw-semibold form-label mt-3">
											<span class="required">Password Confirmation</span>
											<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
												title="Password confirmation required"></i>
										</label>
										<!--end::Label-->
										<input type="password" placeholder="Enter password confirmation" v-model="resetForm.password_confirmation" class="form-control form-control-solid mb-2"/>
										<div class="text">
											<span class="text-danger"></span>
										</div>

										<button type="submit" class="mt-5 btn btn-primary" :disabled="resetFormError.buttonStatus">{{ resetFormError.buttonText }}</button>
									</form>

								</div>
							</div>
						</div>

					</div>
					<div class="col-sm-12 col-md-6">
						<div class="d-flex justify-content-center" v-if="event.logo_path">
							<img :src="event.logo_path"
								alt="logo" class="img-fluid mt-5 w-200px h-200px" />
						</div>
					</div>
				</div>
			</div>
		</div>

		<div class="row">
			<div class="col-lg-12 event_des" v-html="event.footer_content">
			</div>
		</div>

		<div class="modal fade show d-block voting_modal z-index-1" id="staticBackdrop" data-bs-backdrop="static" data-bs-keyboard="false" role="dialog" area="true"
			tabindex="-1" aria-labelledby="staticBackdropLabel" aria-modal="true" v-if="checkLogin() && votingPopup ">
			<div class="modal-dialog" v-for="(voting, index) in popupVoteGetter" :key="index">
				<div class="modal-content shadow-lg" v-if="voting.showVote.title || voting.showVote.vote || voting.showVote.answer" >
					<div class="card mb-5">
						<!--begin::Header-->
						<div class="card-header">
							<h3 class="card-title fw-bold text-dark">Voting</h3>
							<button v-if="voting.showVote.title && !voting.showVote.vote && voting.showVote.answer || voting.voting_result.Mover && voting.voting_result.Seconder" title="Remove Vote" type="button" class="close btn p-0" @click="removePopupVote(voting)"  style="font-size:25px;color:red;" data-dismiss="modal" aria-label="Close">
								<span aria-hidden="true">&times;</span>
							</button>
						</div>

						<!--end::Header-->
						<!--begin::Body-->
						<div class="card-body pt-2 pb-0">
							<div>
								<div v-if="voting.showVote.title">
									<h1 class="text-center">{{ voting.title }}</h1>
								</div>

								<div class="pb-5" v-if="voting.showVote.vote">


									<div class="d-flex justify-content-center align-centrer flex-row mt-3 mb-3" v-if="voting.voting_type == 'AGM'">

										<div class="" v-if="voting.info.agm_type == 1">
											<button type="button" class="btn btn-success me-2 mb-2" @click="sendAGMVote('yes', voting)">{{ voting.info.input_fields.Yes[langdata.locale]??translates.Yes }}</button>
											<button type="button" class="btn btn-danger me-2 mb-2" @click="sendAGMVote('no', voting)">{{ voting.info.input_fields.No[langdata.locale]??translates.No }}</button>
											<button type="button" class="btn btn-primary mb-2" @click="sendAGMVote('abstaining', voting)">{{ voting.info.input_fields.Abstaining[langdata.locale]??translates.Abstaining }}</button>
										</div>


										<div v-if="voting.info.agm_type == 2">
											<button type="button" :class="voting.voting_result.Mover?'btn me-2 mb-2 btn-secondary':'btn me-2 mb-2 btn-primary'" v-if="voting.info.mover == 1 && !voting.voting_result.Mover && !voting.voting_result.Secon || voting.info.mover == 1 && voting.voting_result.Mover && !voting.voting_result.Seconder" @click="sendAGMVote('mover', voting)" :disabled="voting.voting_result.Mover">{{ translates.Mover }}</button>
											<button type="button" :class="(voting.voting_result.Seconder || !voting.voting_result.Mover)?'btn btn-secondary mb-2':'btn btn-danger mb-2'" v-if="voting.info.secondary == 1 && !voting.voting_result.Mover && !voting.voting_result.Secon || voting.info.secondary == 1 && voting.voting_result.Mover && !voting.voting_result.Seconder" @click="sendAGMVote('seconder', voting)" :disabled="voting.voting_result.Seconder || !voting.voting_result.Mover">{{ translates.Seconder }}</button>
										</div>
									</div>

									<div class="mt_30" v-if="voting.voting_type == 'Poll' || voting.voting_type == 'Survey'">
										<div class="input-group mb-3 " v-for="(input, index) in votingInputs[voting.id]" :key="index">
											<div class="d-flex flex-column w-100" v-if="input.type == 'text'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<input type="text" class="bg-light w-100 form-control" v-model="votingForm[voting.id][input.name]">
											</div>
											<div class="d-flex flex-column w-100" v-if="input.type == 'number'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<input type="number" class="bg-light w-100 form-control" v-model="votingForm[voting.id][input.name]">
											</div>

											<div class="d-flex flex-column w-100" v-if="input.type == 'textarea'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<textarea class="bg-light form-control" aria-label="With textarea" v-model="votingForm[voting.id][input.name]"></textarea>
											</div>

											<div class="d-flex flex-column w-100" v-if="input.type == 'radio-group'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<div class="d-flex flex-row w-100 flex-wrap">
													<div class="form-check form-check-custom form-check-solid me-10 pb_10 w-100" v-for="(option, i) in input.values" :key="i">
														<input class="form-check-input h-20px w-20px" type="radio" v-model="votingForm[voting.id][input.name]"
															:value="option.value" :id="option.value.split(' ').join('_')" />
														<label class="form-check-label">
															{{ option.label }}
														</label>
													</div>
												</div>
											</div>

											<div class="d-flex flex-column w-100" v-if="input.type == 'checkbox-group'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<div class="d-flex flex-row w-100 flex-wrap">
													<div class="form-check form-check-custom form-check-solid me-10 pb_10 w-100" v-for="(option, i) in input.values" :key="i">

														<input class="form-check-input h-20px w-20px" type="checkbox" v-model="votingForm[voting.id][input.name][option.label]" @change="handleCheckboxChange(voting.info, votingForm[voting.id][input.name], option)"
														:value="option.value" :id="'checkbox_'+ option.value.split(' ').join('_')" />
														<label class="form-check-label">
															{{ option.label }}
														</label>
													</div>
												</div>

											</div>
											<div class="d-flex flex-column w-100" v-if="input.type == 'select'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<div class="d-flex flex-row w-100">
													<select v-model="votingForm[voting.id][input.name]"
														class="form-select" data-placeholder="Select language" :multiple="input.multiple">
														<option :value="option.value" v-for="(option, i) in input.values" :selected="option.selected" :key="i">{{ option.label }}</option>
													</select>
												</div>

											</div>

											<div class="d-flex flex-column w-100" v-if="input.type == 'file'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<div class="d-flex flex-row w-100">
													<input type="file" class="w-100 form-control"
														aria-describedby="inputGroupFileAddon03" aria-label="Upload">
												</div>

											</div>

											<div class="d-flex flex-column w-100" v-if="input.type == 'date'">
												<label class="p-0 fw-semibold form-label" v-if="input.label.trim()">
													<span :class="{required : input.required}" v-html="input.label"></span>
													<i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
													:title="`${getContentFromDiv(input.label)}`"></i>
												</label>
												<!--begin::Input-->
												<div class="position-relative align-items-center">
													<!--begin::Icon-->
													<!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
													<span class="svg-icon svg-icon-2 position-absolute mx-4 mt_12">
														<svg width="24" height="24" viewBox="0 0 24 24"
															fill="none" xmlns="http://www.w3.org/2000/svg">
															<path opacity="0.3"
																d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
																fill="currentColor" />
															<path
																d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
																fill="currentColor" />
															<path
																d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
																fill="currentColor" />
														</svg>
													</span>
													<!--end::Svg Icon-->
													<!--end::Icon-->
													<!--begin::Datepicker-->
													<input name="start_time"
														class="form-control form-control-solid ps-12 custom_date"
														:placeholder="translates.Select_a_date" v-model="votingForm[voting.id][input.name]" type="date" />
													<!--end::Datepicker-->
												</div>
												<!--end::Input-->
											</div>
											<div class="text" v-if="votingFormError[voting.id]">
												<span class="text-danger">{{ votingFormError[voting.id][input.name] }}</span>
											</div>
											<!-- for voting form paragraph-->
											<div v-if="input.type == 'paragraph'" v-html="input.label"></div>
										</div>
										<button type="button" class="mt-5 btn btn-primary" @click="pollSubmit(voting)">{{translates.Submit}}</button>
									</div>

								</div>

							</div>
						</div>
						<!--end::Body-->
					</div>
					<div class="voting px-5 pb-5" v-if="voting.showVote.answer && votingResult[voting.id]">
						<h2 class="text-center">{{ translates.Voting_Result }}</h2>
						<ul class="list-group">
							<li class="list-group-item d-flex justify-content-between">
								<p class="form-label">{{ translates.Total_Vote }} </p>
								<p class="form-label">{{ votingResult[voting.id].total_vote }}</p>
							</li>
							<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
								<p class="form-label">{{ voting.info.input_fields.Yes[langdata.locale]??translates.Yes }} </p>
								<p class="form-label">{{ votingResult[voting.id].Yes }}%</p>
							</li>
							<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
								<p class="form-label">{{ voting.info.input_fields.No[langdata.locale]??translates.No }} </p>
								<p class="form-label">{{ votingResult[voting.id].No }}%</p>
							</li>
							<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 1">
								<p class="form-label">{{ voting.info.input_fields.Abstaining[langdata.locale]??translates.Abstaining }} </p>
								<p class="form-label">{{ votingResult[voting.id].Abstaining }}%</p>
							</li>
							<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 2">
								<p class="form-label">{{ translates.Mover }} </p>
								<p class="form-label">{{ votingResult[voting.id].mover_voter_name }}</p>
							</li>
							<li class="list-group-item d-flex justify-content-between" v-if="voting.voting_type =='AGM' && voting.info.agm_type === 2">
								<p class="form-label">{{ translates.Seconder }} </p>
								<p class="form-label">{{ votingResult[voting.id].seconder_voter_name }}</p>
							</li>
							<div  v-if="voting.voting_type =='Poll' || voting.voting_type =='Survey'">
								<li class="list-group-item d-flex justify-content-between" v-for="(r_field, c_i) in votingResult[voting.id]['custom_voting_data']" :key="c_i">
									<!-- {{ r_field }} -->
									<div class="row w-100">
										<div class="col-lg-5">
											<p class="form-label" v-html="r_field.label"></p>
										</div>
										<div class="col-lg-7">
											<p class="d-flex justify-content-between" v-for="(value, t_i) in r_field.values" :key="t_i">
												<strong>{{t_i}} : </strong> <span class="text-nowrap">{{ value }} %</span>
											</p>
										</div>
									</div>
								</li>
							</div>
						</ul>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>
<script>
	import axios from 'axios';
	import moment from 'moment-timezone';
	import { ref, onMounted, onUpdated, onUnmounted, computed } from "vue";
	import Avatar from 'vue-avatar-component';
	import Menu from './Menus/EventMenu.vue';
	import {getShowLoginOrRegister, checkAllowRegister} from '../utils/eventHelpers';
	import {getContentFromDiv, formatDateTime, votingSidebarWidth, votingSidebarWidthOpposide} from '../utils/generalHelper';
	export default{
		props:['event','user', 'translates', 'getvoting', 'livechat', 'landingpage', 'menus', 'langdata'],
		components: { Avatar, Menu },

		setup(props) {
			const now = new Date();
    		const newYear = new Date(props.event.start_time);
			const login_form = JSON.parse(props.event.login_form.form_data[props.langdata.locale]);
			const register_form = JSON.parse(props.event.register_form.form_data[props.langdata.locale]);

			const token = ref(document.head.querySelector('meta[name="_token"]').content);
			const popupVotings = ref({});
			const sidebarVotings = ref({});
			const answers = ref([]);
			const givenVoteID = ref([]);
			const notGivenVoteID = ref([]);
			const send_question = ref({
				question:'',
				is_anonymous: false
			});
			const questionError = ref('');
			const loginForm = ref({
				_token: '',
				event_id:''
			});
			const registerForm = ref({
				_token: '',
				event_id:''
			});
			const messageUnderDisplay = ref('');
			const votingInputs = ref({});
			const votingForm = ref({});
			const votingFormError = ref({});
			const votingResult = ref({});
			const loginInputs = ref(login_form);
			const loginFormError = ref({});
			const registerInputs = ref(register_form);
			const registerFormError = ref({});
			const showSections = ref(1);
			const showLogin = ref(getShowLoginOrRegister(props.event)?.showLogin);
			const registerRequired = ref(checkAllowRegister(props.event));
			const showRegister = ref(getShowLoginOrRegister(props.event)?.showRegister);
			const showForgot = ref(0);
			const showReset = ref(0);
			const time = ref(newYear - now);
			const collapsed = ref(true);
			const forgotForm = ref({
				_token: '',
				email:'',
				event_id:''
			});
			const forgotFormError = ref({
				sendButtonText: 'Send link',
				sendButtonStatus: false
			});
			const resetForm = ref({
				_token: '',
				email:'',
				event_id:'',
				password: '',
				password_confirmation:'',
				token: ''
			});
			const resetFormError = ref({
				buttonText: 'Submit',
				buttonStatus: false
			});

			const votingPopup = ref(false);
			const send_message = ref({
				message:''
			});
			const sendMessageError = ref({
				message:''
			});
			const messages = ref([]);
			const messagePage = ref(1);
			const messageLastPage = ref(1);
			const answerPage = ref(1);
			const answerLastPage = ref(1);

			let hasScrolledToBottom = ref("");

			//methods
			const isVoteGiven = (votingId) => {
				const found = givenVoteID.value.find((element) => element == votingId);
				if(found){
					return true;
				}else{
					return false;
				}
			};
			const getVoteIdGiven = () => {
				let data = {
					_token: token.value,
					event_id: props.event.id,
				}
				//this.givenVoteID = [3,4,5,5];
				axios.get(`/get-all-given-vote/${props.event.id}`).then((res) => {
					givenVoteID.value = res.data[0];
				});
			};
			const removePopupVote = (voting) => {
				delete votingForm.value[voting];
				delete votingResult.value[voting.id];
				if(voting.display_type == 'Popup'){
					delete popupVotings.value[voting.id];
					if(Object.keys(popupVotings.value).length < 1 && votingPopup.value){
						votingPopup.value = false;
					}
				}
			};
			const emailCheck = (input) => {
				if(input.name == 'email' && !props.event.is_membership_number || input.name == 'membership_number' && !props.event.is_membership_number){
					return true;
				}
				return false;
			};
			const memberCheck = (input) => {
				if(input.name == 'email' && props.event.is_membership_number || input.name == 'membership_number' && props.event.is_membership_number){
					return true;
				}
				return false;
			};
			const getMessages = async () => {
				if(props.livechat && props.event.allow_msg && checkLogin()){
					let data = {
						_token: token.value,
						eventable_id: props.event.id,
						eventable_type: 'event'
					}

					await axios.post('/messages/get-event-user-message', data).then((res) => {
						messages.value = res.data.data.sort().reverse();
						messagePage.value = res.data.current_page;
						messageLastPage.value = res.data.last_page;
					});
				}
			};
			const getNextMessages = () => {
				if(props.user && props.livechat && props.event.allow_msg && checkLogin()){
					const message_div = document.querySelector('#message_div');
					if(message_div){
						message_div.addEventListener('scroll', e => {
							if (message_div.scrollTop === 0) {
								let data = {
									_token: token.value,
									eventable_id: props.event.id,
									eventable_type: 'event',
									page:messagePage.value + 1
								}
								if(messageLastPage.value >= messagePage.value + 1){
									axios.post('/messages/get-event-user-message', data).then((res) => {
										messages.value.unshift(...res.data.data.sort().reverse());
										messagePage.value = res.data.current_page;
										messageLastPage.value = res.data.last_page;
									});
								}
							}
						});
					}
				}
			};
			const sendMessage = () => {
				if(checkLogin()){
					sendMessageError.value.message = '';
					let data ={
						_token: token.value,
						eventable_id: props.event.id,
						eventable_type: 'event',
						message: send_message.value.message,
						name: props.user.first_name??'' +' '+ props.user.last_name??'',
						sender_type: 'event_user',
						receiveble_id: props.event.user_id
					}
					if(send_message.value.message == ''){
						sendMessageError.value.message = 'The message is required.'
						return false;
					}
					axios.post('/messages/send-message', data).then((res) => {
						messages.value.push(res.data.message);
						send_message.value.message = '';
					});
				}
			};
			const showRegisterForm = () => {
				showLogin.value = 0;
				showRegister.value = 1;
				showForgot.value = 0;
				showReset.value = 0;
				initSelect2();
			};
			const showLoginForm = () => {
				showSections.value = 0;
				showLogin.value = 1;
				showRegister.value = 0;
				showForgot.value = 0;
				showReset.value = 0;
				initSelect2();
			};
			const showForgotPassword = () => {
				showLogin.value = 0;
				showRegister.value = 0;
				showForgot.value = 1;
				showReset.value = 0;
			};
			const checkClassExist = (it, data) =>{
				return data?.find(item => item == it)
			};
			const checkVoting = async () => {
				var unsubmitted_votes = [];
				if(props.user && checkLogin()){
					messageUnderDisplay.value = props.event.message_under_video;
					if(props.event.allow_poll){

						props.getvoting.forEach((voting) => {
							console.log('voting => ',voting);
							if(voting.curent_user_vote){
								givenVoteID.value.push(voting.id)
							}else{
								unsubmitted_votes.push(parseInt(voting.id));
							}
							votingResult.value[voting.id] = voting.voting_result;
							// if(voting && !voting.curent_user_vote){
							if(voting){
								let user_classes = JSON.parse(props.user.class);
								let primary_list = JSON.parse(voting.class);
								let is_class_exist = false;
								if(primary_list && primary_list.length){
									primary_list.map((i) => {
										if (checkClassExist(i, user_classes)) {
											is_class_exist = true;
										}
									});
								}
								// if(this.user.voter == 'No' || voting.access_type == 'Restricted' &&  !is_class_exist){
								if(props.user.voter == 'No' && (voting.access_type == 'Open' || voting.access_type == 'Restricted') || voting.access_type == 'Restricted' &&  !is_class_exist){
									let showVote = {
										vote :false,
										title : true,
										answer : true
									};
									if(voting.is_display_result_everyone){
										if(voting.display_type == 'Popup'){
											votingPopup.value = true;
											popupVotings.value[voting.id] = voting;
											popupVotings.value[voting.id].title = voting.title[props.langdata.locale]??voting.title[voting.owner.lang];
											popupVotings.value[voting.id].showVote = showVote;
										}
										else if(voting.display_type == 'Sidebar'){
											collapsed.value = false;
											sidebarVotings.value[voting.id] = voting;
											sidebarVotings.value[voting.id].title = voting.title[props.langdata.locale]??voting.title[voting.owner.lang];
											sidebarVotings.value[voting.id].showVote = showVote;
										}
										votingResult.value[voting.id] = voting.voting_result;
									}

								}else{
									let showVote = {
										vote :false,
										title : false,
										answer : false
									};
									if(voting.is_display_result_everyone || parseInt(voting.is_display_result) == 1){
										showVote.title = true;
										showVote.answer = true;
										if(voting.curent_user_vote == null && voting.display_live){
											showVote.vote = true;
										}
									}else if(voting.display_live){
										if(voting.curent_user_vote == null){
											showVote.vote = true;
											showVote.title = true;
											if(voting.is_display_result){
												showVote.answer = true;
											}
										}
									}
									if(voting.display_type == 'Popup'){
										if(!showVote.vote && !showVote.title && !showVote.answer){
											votingPopup.value = false;
										}else{
											votingPopup.value = true;
										}
										popupVotings.value[voting.id] = voting;
										popupVotings.value[voting.id].title = voting.title[props.langdata.locale]??voting.title[voting.owner.lang];
										popupVotings.value[voting.id].showVote = showVote;
									}
									else if(voting.display_type == 'Sidebar'){
										collapsed.value = false;
										sidebarVotings.value[voting.id] = voting;
										sidebarVotings.value[voting.id].title = voting.title[props.langdata.locale]??voting.title[voting.owner.lang];
										sidebarVotings.value[voting.id].showVote = showVote;
									}

									votingResult.value[voting.id] = voting.voting_result;
									hideVoteSetTime(voting);

									if(voting.voting_type == 'Poll' || voting.voting_type == 'Survey'){
										votingInputs.value[voting.id] = voting.info.input_fields;
										votingFormError.value[voting.id] = {};

										votingForm.value[voting.id] = {};
										votingInputs.value[voting.id].forEach(element => {
											votingFormError.value[voting.id][element.name] = '';
											if(element.value){
												votingForm.value[voting.id][element.name] = element.value;
											}
											else if(element.values){
												if(element.type == 'radio-group'){
													votingForm.value[voting.id][element.name] = '';
													element.values.forEach(option => {
														if(option.selected){
															votingForm.value[voting.id][element.name] = option.value;
														}
													});
												}
												else if(element.type == 'checkbox-group'){
													votingForm.value[voting.id][element.name] = {};

													element.values.forEach((option) => {
														if(option.selected){
															votingForm.value[voting.id][element.name][option.label] = true;
														}else{
															votingForm.value[voting.id][element.name][option.label] = false;
														}
													});
												}
												else if(element.type == 'select'){
													if(element.multiple){
														votingForm.value[voting.id][element.name] = {};
														element.values.forEach(option => {
															if(option.selected){
																votingForm.value[voting.id][element.name][option.label] = true;
															}else{
																votingForm.value[voting.id][element.name][option.label] = false;
															}
														});
													}else{
														votingForm.value[voting.id][element.name] = '';
														element.values.forEach(option => {
															if(option.selected){
																votingForm.value[voting.id][element.name] = option.value;
															}
														});
													}
												}
											}
											else{
												votingForm.value[voting.id][element.name] = '';
											}
										});
									}
								}


							}
						});
					}
				}
				localStorage.setItem(`unsubmitted_${props.event.id}`, unsubmitted_votes);
			};
			const getAnswer = async () => {
				if(checkLogin()){
					if(props.event.allow_ans_msg){
						axios.get('/getanswer-by-event?id='+props.event.id).then((res) => {
							answers.value = res.data.answers.data;
							answerPage.value = res.data.answers.current_page;
							answerLastPage.value = res.data.answers.last_page;
						});
					}
				}
			};

			const getNextAnswers = () => {
				if(props.user && checkLogin()){
					const answer_div = document.querySelector('#answer_list');
					if(answer_div){
						answer_div.addEventListener('scroll', e => {
							if ((answer_div.scrollTop + answer_div.clientHeight) == answer_div.scrollHeight) {
								if(answerLastPage.value >= answerPage.value + 1){
									axios.get('/getanswer-by-event?id='+props.event.id+'&page='+(answerPage.value + 1)).then((res) => {
										if (res.data.answers) {
											answers.value.push(...res.data.answers.data);
											answerPage.value = res.data.answers.current_page;
											answerLastPage.value = res.data.answers.last_page;
										}
									});
								}
							}
						});
					}
				}
			};
			const sendAGMVote = (data_type, voting) => {
				let data = {
					_token: token.value,
					voting_type: voting.voting_type,
					data_type: data_type,
					agm_type: voting.info.agm_type,
					id: voting.id,
					eventable_id: props.event.id,
					eventable_type: 'event'
				}
				axios.post('/send-vote', data).then((res) => {
					voting = res.data.voting;
					if(res.data.vote_given == true){
						toastr.warning('Vote has submitted already.', 'Warning');
					}else{
						toastr.success(props.translates.Submitted_successfully, 'Success');
						notGivenVoteID.value = notGivenVoteID.value.filter((value) => value!= voting.id.toString());
						localStorage.setItem('unsubmitted_'+props.event.id, notGivenVoteID.value);
					}
					givenVoteID.value.push(voting.id);
					if(parseInt(voting.is_display_result) == 1 || parseInt(voting.is_display_result_everyone) == 1){
						votingResult.value[voting.id] = voting.voting_result;
					}else{
						delete votingResult.value[voting.id];
					}
					if(voting.display_type == 'Popup'){
						if(voting.is_display_result_everyone == 1 || voting.is_display_result == 1){
							let showVote = {
										vote :false,
										title : true,
										answer : true
									};
							popupVotings.value[voting.id].showVote = showVote;
						}else{
							delete popupVotings.value[voting.id];
							if(Object.keys(popupVotings.value).length < 1 & votingPopup.value){
								votingPopup.value = false;
							}
						}
					}
					else if(voting.display_type == 'Sidebar'){
						if(voting.is_display_result_everyone == 1 || voting.is_display_result == 1){
							let showVote = {
										vote :false,
										title : true,
										answer : true
									};
							sidebarVotings.value[voting.id].showVote = showVote;
						}else{
							delete sidebarVotings.value[voting.id];
						}
					}
				});
			};
			const pollSubmit = (voting) => {
				let data = {
					_token: token.value,
					voting_type: voting.voting_type,
					agm_type: voting.info.agm_type,
					id: voting.id,
					eventable_id: props.event.id,
					eventable_type: 'event',
					inputs: votingForm.value[voting.id]
				}

				let val = false;
				votingFormError.value[voting.id] = [];
                var form_data = voting.info.input_fields;

				form_data.forEach(element => {
					if(element.required && element.type != 'checkbox-group' && !votingForm.value[voting.id][element.name]){
						val = true;
						votingFormError.value[voting.id][element.name] = 'The ' + element.label + ' field is required.';
					}else if(element.type == 'checkbox-group' && element.required){
						var checkbox_val = true;
						for (let key in votingForm.value[voting.id][element.name]) {
							if (votingForm.value[voting.id][element.name].hasOwnProperty(key)) {
								if(votingForm.value[voting.id][element.name][key]){
									checkbox_val = false;
								}
							}
						}
						if(checkbox_val){
							val = true;
							votingFormError.value[voting.id][element.name] = 'The ' + element.label + ' field is required.';
						}
					}
				});

				if(val){
					return false;
				}

				axios.post('/send-vote', data).then((res) => {
					voting = res.data.voting;
					if(res.data.vote_given == true){
						toastr.warning('Vote has submitted already.', 'Warning');
					}else{
						toastr.success(props.translates.Submitted_successfully, 'Success');
						notGivenVoteID.value = notGivenVoteID.value.filter((value) => value!= voting.id.toString());
						localStorage.setItem('unsubmitted_'+props.event.id, notGivenVoteID.value);
					}
					givenVoteID.value.push(voting.id);
					if(parseInt(voting.is_display_result) == 1 || parseInt(voting.is_display_result_everyone) == 1){
						votingResult.value[voting.id] = voting.voting_result;
					}else{
						delete votingResult.value[voting.id];
					}
					if(voting.display_type == 'Popup'){
						if(voting.is_display_result_everyone == 1 || voting.is_display_result == 1){
							let showVote = {
										vote :false,
										title : true,
										answer : true
									};
							popupVotings.value[voting.id].showVote = showVote;
						}else{
							delete popupVotings.value[voting.id];
							if(Object.keys(popupVotings.value).length < 1 & votingPopup.value){
								votingPopup.value = false;
							}
						}

					}
					else if(voting.display_type == 'Sidebar'){
						if(voting.is_display_result_everyone == 1 || voting.is_display_result == 1){
							let showVote = {
										vote :false,
										title : true,
										answer : true
									};
							sidebarVotings.value[voting.id].showVote = showVote;
						}else{
							delete sidebarVotings.value[voting.id];
						}
					}
				});
			};
			const sendForgotLink = async () => {
				forgotForm.value._token = token.value;
				forgotForm.value.event_id = props.event.id;
				forgotFormError.value.error = '';
				forgotFormError.value.sendButtonText = 'Sending! Please wait...'
				forgotFormError.value.sendButtonStatus = true;

				axios.post('/event-user/send-reset-link', forgotForm.value).then((res) => {
					if(res.data.message == 'success'){
						toastr.success('Reset link already sent to your mail.', 'Success');
						forgotForm.value.email = '';
						forgotFormError.value.sendButtonText = 'Send link';
						forgotFormError.value.sendButtonStatus = false;
					}
				}).catch((error) => {
					if(error.response.data.message){
						forgotFormError.value.error = error.response.data.message
					}
					forgotFormError.value.sendButtonText = 'Send link';
					forgotFormError.value.sendButtonStatus = false;
				});

			};

			const checkForgotPassword = () => {
				let url = window.location.search;
				if(url){
					url = new URLSearchParams(url);
					let data = {};
					for (const [key, value] of url) {
						data[key] = value;
					}

					if(data.token){
						showForgot.value = 0;
						showLogin.value = 0;
						showRegister.value = 0;
						showReset.value = 1;
						resetForm.value.email = data.email;
						resetForm.value.token = data.token;
					}

				}
			};

			const resetPassword = () => {
				resetForm.value._token = token.value;
				resetForm.value.event_id = props.event.id;
				resetFormError.value.email = '';
				resetFormError.value.password = '';
				resetFormError.value.password_confirmation = '';

				resetFormError.value.buttonText = 'Submiting! Please wait...'
				resetFormError.value.buttonStatus = true;

				axios.post('/event-user/reset-password', resetForm.value).then((res) => {
					if(res.data.message == 'Success'){
						toastr.success('Password reset successfully!', 'Success');
						resetForm.value.email = '';
						resetForm.value.password = '';
						resetForm.value.password_confirmation = '';
						setTimeout(() => {
							window.location.href = res.data.link;
							resetFormError.value.buttonText = 'Submit'
							resetFormError.value.buttonStatus = false;
						}, 400);
					}
				}).catch((error) => {
					if(error.response.data.message){
						resetFormError.value.email = error.response.data.message
					}
					resetFormError.value.buttonText = 'Submit'
					resetFormError.value.buttonStatus = false;
				});
			};
			const checkWebsocketForVoting = () => {
				if(props.user && checkLogin()){
					Echo.private(`event-voting-channel.${props.event.id}`)
					.listen('SendEventVoting', (e) => {
						if(props.event.allow_poll){
							//if(parseInt(e.voting.display_live) === 1 && !e.voting.curent_user_vote){
							if(parseInt(e.voting.display_live) === 1 && !isVoteGiven(e.voting.id) && e.voting.current_voter_id != props.user.id){
								var unsubmitted_votes = localStorage.getItem('unsubmitted_'+props.event.id);
								if(unsubmitted_votes){
									if(!notGivenVoteID.value.includes(e.voting.id.toString())){
										notGivenVoteID.value.push(e.voting.id.toString());
									}
									localStorage.setItem('unsubmitted_'+props.event.id, notGivenVoteID.value);
								}
								let user_classes = JSON.parse(props.user.class);
								let primary_list = JSON.parse(e.voting.class);
								let is_class_exist = false;
								if(primary_list && primary_list.length){
									primary_list.map((i) => {
										if (checkClassExist(i, user_classes)) {
											is_class_exist = true;
										}
									});
								}

								// if(this.user.voter == 'No' || e.voting.access_type == 'Restricted' && !is_class_exist){
								if(props.user.voter == 'No' && (e.voting.access_type == 'Open' || e.voting.access_type == 'Restricted') || e.voting.access_type == 'Restricted' && !is_class_exist){
									let showVote = {
										vote :false,
										title : true,
										answer : true
									};
									if(e.voting.is_display_result_everyone == 1){
										if(e.voting.display_type == 'Popup'){
											votingPopup.value = true;
											popupVotings.value[e.voting.id] = e.voting;
											popupVotings.value[e.voting.id].title = e.voting.title[props.langdata.locale]??e.voting.title[e.voting.owner.lang]
											popupVotings.value[e.voting.id].showVote = showVote;
										}
										else if(e.voting.display_type == 'Sidebar'){
											collapsed.value = false;
											sidebarVotings.value[e.voting.id] = e.voting;
											sidebarVotings.value[e.voting.id].title = e.voting.title[props.langdata.locale]??e.voting.title[e.voting.owner.lang]
											sidebarVotings.value[e.voting.id].showVote = showVote;
										}
									}else{
										delete votingForm.value[e.voting];
										delete votingResult.value[e.voting.id];
										if(e.voting.display_type == 'Popup'){
											delete popupVotings.value[e.voting.id];
											if(Object.keys(popupVotings.value).length < 1 && votingPopup.value){
												votingPopup.value = false;
											}
										}
										else if(e.voting.display_type == 'Sidebar'){
											delete sidebarVotings.value[e.voting.id];
											if(Object.keys(sidebarVotings.value).length < 1){
												collapsed.value = true;
											}
										}
									}

								}else{
									if(e.voting.voting_type == 'Poll' || e.voting.voting_type == 'Survey'){
										votingInputs.value[e.voting.id] = e.voting.info.input_fields;
										votingForm.value[e.voting.id] = {};
										votingInputs.value[e.voting.id].forEach(element => {
											if(element.value){
												votingForm.value[e.voting.id][element.name] = element.value;
											}
											else if(element.values){
												if(element.type == 'radio-group'){
													votingForm.value[e.voting.id][element.name] = '';
													element.values.forEach(option => {
														if(option.selected){
															votingForm.value[e.voting.id][element.name] = option.value;
														}
													});
												}
												else if(element.type == 'checkbox-group'){
													votingForm.value[e.voting.id][element.name] = {};
													element.values.forEach(option => {
														if(option.selected){
															votingForm.value[e.voting.id][element.name][option.label] = true;
														}else{
															votingForm.value[e.voting.id][element.name][option.label] = false;
														}
													});
												}
												else if(element.type == 'select'){
													if(element.multiple){
														votingForm.value[e.voting.id][element.name] = {};
														element.values.forEach(option => {
															if(option.selected){
																votingForm.value[e.voting.id][element.name][option.label] = true;
															}else{
																votingForm.value[e.voting.id][element.name][option.label] = false;
															}
														});
													}else{
														votingForm.value[e.voting.id][element.name] = '';
														element.values.forEach(option => {
															if(option.selected){
																votingForm.value[e.voting.id][element.name] = option.value;
															}
														});
													}
												}
											}
											else{
												votingForm.value[e.voting.id][element.name] = '';
											}
										});
									}
									let showVote = {
										vote :false,
										title : false,
										answer : false
									};
									if(e.voting.is_display_result_everyone){
										showVote.title = true;
										showVote.answer = true;
										// if(e.voting.curent_user_vote == null && e.voting.display_live){
										if(e.voting.current_voter_id != props.user.id && e.voting.display_live){
											showVote.vote = true;
										}
									}else if(e.voting.display_live){
										if(e.voting.current_voter_id != props.user.id){
											showVote.vote = true;
											showVote.title = true;
											if(e.voting.is_display_result){
												showVote.answer = true;
											}
										}
									}
									if(e.voting.display_type == 'Popup'){
										if(!showVote.vote && !showVote.title && !showVote.answer){
											votingPopup.value = false;
										}else{
											votingPopup.value = true;
										}
										popupVotings.value[e.voting.id] = e.voting;
										popupVotings.value[e.voting.id].title = e.voting.title[props.langdata.locale]??e.voting.title[e.voting.owner.lang];
										popupVotings.value[e.voting.id].showVote = showVote;
									}
									else if(e.voting.display_type == 'Sidebar'){
										collapsed.value = false;
										sidebarVotings.value[e.voting.id] = e.voting;
										sidebarVotings.value[e.voting.id].title = e.voting.title[props.langdata.locale]??e.voting.title[e.voting.owner.lang];
										sidebarVotings.value[e.voting.id].showVote = showVote;
									}
								}
								initSelect2();
								hideVoteSetTime(e.voting);
								if(parseInt(e.voting.is_display_result) === 1 || parseInt(e.voting.is_display_result_everyone) === 1){
									votingResult.value[e.voting.id] = e.voting.voting_result;
								}else{
									delete votingResult.value[e.voting.id];
								}
							}else{
								//if(parseInt(e.voting.display_live) === 1 && e.voting.curent_user_vote && e.voting.curent_user_vote.event_user_id != this.user.id){
								if(false){
								}else{
									if(parseInt(e.voting.is_display_result_everyone) === 1 || parseInt(e.voting.is_display_result) == 1){
										let showVote = {
											vote :false,
											title : true,
											answer : true
										};
										if(e.voting.display_type == 'Popup'){
											votingPopup.value = true;
											popupVotings.value[e.voting.id] = e.voting;
											popupVotings.value[e.voting.id].title = e.voting.title[props.langdata.locale]??e.voting.title[e.voting.owner.lang];
											popupVotings.value[e.voting.id].showVote = showVote;
										}
										else if(e.voting.display_type == 'Sidebar'){
											collapsed.value = false;
											sidebarVotings.value[e.voting.id] = e.voting;
											sidebarVotings.value[e.voting.id].title = e.voting.title[props.langdata.locale]??e.voting.title[e.voting.owner.lang];
											sidebarVotings.value[e.voting.id].showVote = showVote;
										}
										initSelect2();
										hideVoteSetTime(e.voting);
										if(parseInt(e.voting.is_display_result) == 1 || parseInt(e.voting.is_display_result_everyone) == 1){
											votingResult.value[e.voting.id] = e.voting.voting_result;
										}else{
											delete votingResult.value[e.voting.id];
										}
									}else{
										delete votingForm.value[e.voting];
										delete votingResult.value[e.voting.id];
										if(e.voting.display_type == 'Popup'){
											delete popupVotings.value[e.voting.id];
											if(Object.keys(popupVotings.value).length < 1 && votingPopup.value){
												votingPopup.value = false;
											}
										}
										else if(e.voting.display_type == 'Sidebar'){
											delete sidebarVotings.value[e.voting.id];
											if(Object.keys(sidebarVotings.value).length < 1){
												collapsed.value = true;
											}else{
												collapsed.value = false;
											}
										}
									}
									if(e.voting.current_voter_id == props.user.id){
										givenVoteID.value.push(e.voting.id);
									}

								}
							}
						}
					});
				}
			};
			const renderAuthForm = () =>{
				loginInputs.value.forEach(element => {
					if(element.values){
						if(element.type == 'radio-group'){
							element.values.forEach(option => {
								if(option.selected){
									loginForm.value[element.name] = option.value;
								}
							});
						}
						else if(element.type == 'checkbox-group'){
							loginForm.value[element.name] = {};
							element.values.forEach(option => {
								if(option.selected){
									loginForm.value[element.name][option.label] = true;
								}else{
									loginForm.value[element.name][option.label] = false;
								}
							});
						}
						else if(element.type == 'select'){
							if(element.multiple){
								loginForm.value[element.name] = {};
								element.values.forEach(option => {
									if(option.selected){
										loginForm.value[element.name][option.label] = true;
									}else{
										loginForm.value[element.name][option.label] = false;
									}
								});
							}else{
								loginForm.value[element.name] = '';
								element.values.forEach(option => {
									if(option.selected){
										loginForm.value[element.name] = option.value;
									}
								});
							}
						}
					}
				});

				registerInputs.value.forEach(element => {
					if(element.values){
						if(element.type == 'radio-group'){
							element.values.forEach(option => {
								if(option.selected){
									registerForm.value[element.name] = option.value;
								}
							});
						}
						else if(element.type == 'checkbox-group'){
							registerForm.value[element.name] = {};
							element.values.forEach(option => {
								if(option.selected){
									registerForm.value[element.name][option.label] = true;
								}else{
									registerForm.value[element.name][option.label] = false;
								}
							});
						}
						else if(element.type == 'select'){
							if(element.multiple){
								registerForm.value[element.name] = {};
								element.values.forEach(option => {
									if(option.selected){
										registerForm.value[element.name][option.label] = true;
									}else{
										registerForm.value[element.name][option.label] = false;
									}
								});
							}else{
								element.values.forEach(option => {
									if(option.selected){
										registerForm.value[element.name] = option.value;
									}
								});
							}
						}
					}
				});

				initSelect2();
			};
			const checkLogin = () => {
				if(props.event.login_required){
					if(props.user != null){
						localStorage.setItem('event_user', JSON.stringify(props.user));
					}else{
						localStorage.removeItem('event_user');
					}

					// this.loginInputs = JSON.parse(this.loginform.form_data);
					// this.registerInputs = JSON.parse(this.registerform.form_data);


					if(localStorage.getItem('event_user')){
						let event_user = JSON.parse(localStorage.getItem('event_user'));
						if(event_user.eventable_id == props.event.id){
							localStorage.removeItem('event_user');
							return true;
						}
					}else{
						return false;
					}
				}else if(showLogin.value && !showSections.value)
				 	return false;
				else{
					return true;
				}


			};
			const Login = async () => {
				loginForm.value._token = token.value;
				loginForm.value.event_id = props.event.id;
				loginForm.value.lang = props.langdata.locale;
				loginFormError.value.error = '';
				if(props.event.password_only && !loginForm.value.password){
					loginForm.value.password = '';
				}

				var form_data = JSON.parse(props.event.login_form.form_data[props.langdata.locale]);
				let val = false;
				loginFormError.value = [];

				form_data.forEach(element => {
					if(element.required && element.name != 'password' && emailCheck(element) && !loginForm.value['email']){
						val = true;
						loginFormError.value['email'] = 'The email field is required.';
					}

					else if(element.required && element.name != 'password' && memberCheck(element) && !loginForm.value['membership_number']){
						val = true;
						loginFormError.value['membership_number'] = 'The '+props.event.custom_label_of_membership_number+' field is required.';
					}

					else if(element.required && element.name != 'password' && !loginForm.value[element.name] && element.name != 'membership_number' && element.name != 'email'){
						val = true;
						loginFormError.value[element.name] = 'The ' + element.label + ' field is required.';
					}
					else if(element.required && element.name == 'password' && props.event.password_only && !loginForm.value[element.name]){
						val = true;
						loginFormError.value[element.name] = 'The ' + element.label + ' field is required.';
					}
				});

				if(val){
					return false;
				}

				await axios.post('/event-user/login', loginForm.value).then((res) => {
					localStorage.setItem('event_user', JSON.stringify(res.data));
					toastr.success(props.translates.Logined_successfully, 'Success');
					setTimeout(() => {
						if(props.landingpage){
							joinTo();
							setTimeout(() => {
								location.href = props.landingpage;
							}, 1000);
						}else{
							location.reload();
						}
					}, (500));

				}).catch((error) => {
					Object.entries(error.response.data.errors).forEach(error => {
						loginFormError.value[error[0]] = error[1][0];
					});
				});
            };
			const register = async () => {
				registerForm.value._token = token.value;
				registerForm.value.event_id = props.event.id;

				var form_data = JSON.parse(props.event.register_form.form_data[props.langdata.locale]);
				let val = false;
				registerFormError.value = [];

				form_data.forEach(element => {
					if(element.required && element.name != 'password' && !registerForm.value[element.name]){
						val = true;
						registerFormError.value[element.name] = 'The ' + element.label + ' field is required.';
					}
					else if(element.required && element.name == 'password' && props.event.password_only && !registerForm.value[element.name]){
						registerFormError.value[element.name] = 'The ' + element.label + ' field is required.';
						val = true;
					}
				});

				if(val){
					return false;
				}

				await axios.post('/event-user/register', registerForm.value).then((res) => {
					toastr.success(props.translates.Registered_successfully, 'Success');
					setTimeout(() => {
						location.reload();
					}, (500));
				}).catch((error) => {

					Object.entries(error.response.data.errors).forEach(error => {
						registerFormError.value[error[0]] = error[1][0];
					});
				});
			};
			const askQuestion = async () => {
				questionError.value = '';
				let data = {
					_token: token.value,
					question : send_question.value.question,
					is_anonymous : props.user?send_question.value.is_anonymous:1,
					id: props.event.id,
					questioner_id: props.user?props.user.id:0
				}
				axios.post('ask-question-for-event', data).then((res) => {
					if(res.status == 201){
						send_question.value.question = '';
						toastr.success(props.translates.Question_Submitted_Successfully,'Success');
					}else{
						toastr.error(props.translates.Something_went_wrong, 'Error');
					}
				}).catch((error) => {
					if(error.response.data){
						questionError.value = error.response.data.message;
					}
				});
			};
			const joinTo = async () => {
				if(checkLogin() && props.user){
					let data = {
						_token: token.value,
						id : props.event.id,
						type: 'event'
					}
					await axios.post('/join-to-event', data, (res) => {

					});
				}
			};
			const timeFormat = (value) => {
            	return moment.tz(value, env_tz).subtract(0, 'minutes').fromNow();
        	};
			const showStartTime = (value) => {
				return formatDateTime(value, 'LL, HH:mm:ss', props.langdata.locale);
				// let time_zone = moment.tz(value, env_tz);
				// return time_zone.format('MMM Do YYYY, h:mm:ss a');
			};
			const checkWebsocketForKickout = () => {
				if(checkLogin() && props.user){
					Echo.private('event-user-kickout-channel-'+props.user.id)
					.listen('KickoutEventUser', (e) => {
						localStorage.removeItem('event_user');
						toastr.success('You ared blocked by admin.', 'Success');
						let data = {
							_token: token.value,
							type:'event',
							id:props.event.id
						};
						let url = $('.logout').attr('href');
						$.post('/leave-from-event', data, function(res){
							location.href = url;
						});
					});
				}
			};

			const checkWebsocketForMessage = () => {
				if(checkLogin() && props.user){
					Echo.private('message-event-channel-'+props.event.id+'-event_users-'+props.user.id)
					.listen('sendMessageForEvent', (e) => {
						messages.value.push(e.message);
					});
				}
			};
			const checkWebsocketForEventMesssage = () => {
				if(checkLogin()){
					Echo.private(`event-question-channel.${props.event.id}`)
					.listen('SendQuestionEvent', (e) => {
						if(e.question.reply){
							answers.value.unshift(e.question);
						}
					});
				}
        	};
			const checkWebsocketForMessageUnderVideo = () => {
				if(checkLogin()){
					Echo.private(`event-under-video-message-push-channel.${props.event.id}`)
					.listen('PushUnderVideoMessageEvent', (e) => {
						messageUnderDisplay.value = e.event.message_under_video;
					});
				}
			};
			const checkWebsocketForEventReload = () => {
				Echo.channel(`event-reload-channel.${props.event.id}`)
				.listen('ReloadEventLive', (e) => {
					if(e.event){
						location.reload();
					}
				});
			};
			const checkWebsocketForQuestionDelete = () => {
				if(checkLogin()){
					Echo.private(`question-delete-event-channel.${props.event.id}`)
					.listen('DeleteQuestionEvent', (e) => {
						getAnswer();
					});
				}
			};
			const collapseVoting = () => {
				collapsed.value = !collapsed.value;
			};
			const showVote = (voting) =>{
                if(voting){
                    let end_time = moment().tz(env_tz).add(voting.display_time, 'seconds');
                    if(voting.display_time > 0 && end_time.isAfter(moment())){
						let showVote = {
							vote :false,
							title : true,
							answer : true
						};
                        if(voting.display_type == 'Popup'){
							if(voting.is_display_result_everyone == 1){
								popupVotings.value[voting.id].showVote = showVote;
							}else{
								delete popupVotings.value[voting.id];
							}

							if(Object.keys(popupVotings.value).length < 1 && votingPopup.value){
								votingPopup.value = false;
							}
						}
						else if(voting.display_type == 'Sidebar'){
							if(voting.is_display_result_everyone){
								sidebarVotings.value[voting.id].showVote = showVote;
							}else{
								delete sidebarVotings.value[voting.id];
							}

							if(Object.keys(sidebarVotings.value).length < 1){
								collapsed.value = true;
							}
						}
					}
                }
            };
			const hideVoteSetTime = (voting) => {
				let server_time = moment.tz(voting.display_live_in, env_tz);
				let current_time = moment().tz(env_tz);
				var duration = moment.duration(server_time.diff(current_time));
				var seconds = duration.asSeconds();
				setTimeout(() => {
					if(voting){
						showVote(voting);
					}
				}, (seconds * 1000));
			};
			const initSelect2 = () => {
				setTimeout(() => {
					$('.form-select-solid').select2();
					$(".custom_date").flatpickr({
						enableTime: true,
						dateFormat: "Y-m-d H:i",
					});
				}, (300));
			};
			const checkEventPresenceChannel = () => {
				if(checkLogin()){
					Echo.join('event-presence-channel-'+props.event.id)
					.here((users) => {

					})
					.joining((res) => {

					})
					.leaving((res) => {

					});
				}

			};
			const handleCheckboxChange = (info, element, option) => {
				var trueCount = Object.values(element).filter(value => value === true).length;
				// Check if the selected items exceed the maximum limit
				if (info.selection_limit_for_checkbox && parseInt(info.selection_limit_for_checkbox) < parseInt(trueCount)) {
					toastr.warning(`Maximum Selection Limit Is: ${info.selection_limit_for_checkbox}`,'Warning');
					element[option.label] = false;
				}
			};
			const scrollBottom = () => {
				if (messages.value.length > 1) {
					let el = hasScrolledToBottom.value;
					el.scrollTop = el.scrollHeight;
				}
			};

			onUpdated(() => {
				scrollBottom();
			});

			onMounted(() => {
				// from created()
				checkVoting();
				checkWebsocketForVoting();
				renderAuthForm();
				checkLogin();
				checkWebsocketForEventMesssage();
				checkWebsocketForMessageUnderVideo();
				checkWebsocketForMessage();
				checkWebsocketForEventReload();
				checkWebsocketForQuestionDelete();
				checkForgotPassword();
				checkWebsocketForKickout();
				scrollBottom();

				var unsubmitted_votes = localStorage.getItem('unsubmitted_'+props.event.id);
				if(unsubmitted_votes){
					notGivenVoteID.value = unsubmitted_votes.split(',');
				}

				// form mounted()
				getMessages();
				getVoteIdGiven();
				getNextMessages();
				getAnswer();
				getNextAnswers();
				joinTo();
				checkEventPresenceChannel();
			});

			// from computed
			// a computed getter
			const popupVoteGetter = computed(() => {
				var pvote= [];
				const sorted = Object.entries(popupVotings.value).sort((a,b)=>a[1].priority-b[1].priority).map((x) => {
					// var a = {};
					// a[x[0]] = x[1]
					//a[x[0]].showVote = showVote
					pvote.push(x[1]);
				});
				return pvote;
			});
			const sidebarVoteGetter = computed(() => {
				var svote= [];
				const sorted = Object.entries(sidebarVotings.value).sort((a,b)=>a[1].priority-b[1].priority).map((x) => {
					// var a = {};
					// a[x[0]] = x[1]
					//a[x[0]].showVote = showVote
					svote.push(x[1]);
				});
				return svote;
			});

			return {
				token,
				popupVotings,
				sidebarVotings,
				answers,
				givenVoteID,
				notGivenVoteID,
				send_question,
				questionError,
				loginForm,
				registerForm,
				messageUnderDisplay,
				votingInputs,
				votingForm,
				votingFormError,
				votingResult,
				loginInputs,
				loginFormError,
				registerInputs,
				registerFormError,
				showSections,
				showLogin,
				registerRequired,
				showRegister,
				showForgot,
				showReset,
				time,
				collapsed,
				forgotForm,
				forgotFormError,
				resetForm,
				resetFormError,
				votingPopup,
				send_message,
				sendMessageError,
				messages,
				messagePage,
				messageLastPage,
				answerPage,
				answerLastPage,
				hasScrolledToBottom,

				isVoteGiven,
				getVoteIdGiven,
				removePopupVote,
				emailCheck,
				memberCheck,
				getMessages,
				getNextMessages,
				sendMessage,
				showRegisterForm,
				showLoginForm,
				showForgotPassword,
				checkClassExist,
				checkVoting,
				getAnswer,
				getNextAnswers,
				sendAGMVote,
				pollSubmit,
				sendForgotLink,
				checkForgotPassword,
				resetPassword,
				checkWebsocketForVoting,
				renderAuthForm,
				checkLogin,
				Login,
				register,
				askQuestion,
				joinTo,
				timeFormat,
				showStartTime,
				checkWebsocketForKickout,
				checkWebsocketForMessage,
				checkWebsocketForEventMesssage,
				checkWebsocketForMessageUnderVideo,
				checkWebsocketForEventReload,
				checkWebsocketForQuestionDelete,
				collapseVoting,
				showVote,
				hideVoteSetTime,
				initSelect2,
				checkEventPresenceChannel,
				handleCheckboxChange,

				popupVoteGetter,
				sidebarVoteGetter,
				formatDateTime,
				getContentFromDiv,
				votingSidebarWidth,
				votingSidebarWidthOpposide
			}
		}
	}
</script>

<style scoped>
	/* .banner_image{
		width: 100%;
		height: auto;
	} */

</style>
