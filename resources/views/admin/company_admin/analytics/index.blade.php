@extends('admin.layouts.layouts')
@section('title')
    {{__("common.Company Dashboard")}}
@endsection

@section('mainContent')
    <div class="d-flex flex-column flex-column-fluid overflow-hidden">
        <!--begin::Toolbar-->
        <div id="" class="py-3 py-lg-6">
            <!--begin::Toolbar container-->
            <div id="kt_app_toolbar_container" class="app-container container-fluid d-flex flex-stack">
                <!--begin::Page title-->
                <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                    <!--begin::Title-->
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('analytics.Analytics')}}</h1>
                    <!--end::Title-->
                </div>
                <!--end::Page title-->
                <!--begin::Actions-->
                @php
                    $selected_webcast = null;
                @endphp
                @if (getCurrentEvent())
                <div class="d-flex align-items-center w-300px">
                    <p class="mt-3 me-5 text-nowrap fw-bolder">{{__('messaging.Webcast')}}</p>
                    <!--begin::Input group-->
                    {{-- @dd(session()->get('selectWebcast')) --}}
                    <div class="position-relative w-md-100 me-md-2">
                        <select class="form-select form-select-custome" data-control="select2" data-placeholder="{{__('messaging.Webcast')}}" id="webcast_list">
                            <option></option>
                            @foreach($webcasts as $webcast)
                            <option @if(request()->get('webcast') && $webcast->id == base64_decode(request()->get('webcast'))) selected @elseif(!session()->get('selectWebcast') && @$webcasts[0]->id == $webcast->id) selected @endif value="{{base64_encode($webcast->id)}}">{!! textLimit($webcast->title, 40) !!}</option>
                            @php
                                if(!session()->get('selectWebcast') && @$webcasts[0]->id == $webcast->id){
                                    $selected_webcast = $webcast->id;
                                }
                            @endphp
                            @endforeach
                        </select>
                    </div>
                    @if(request()->has('webcast') || $selected_webcast)
                    <a href="{{url('/company/analytics')}}" data-id="3" class="btn btn-icon btn-color-muted btn-bg-light btn-active-color-primary btn-sm data_delete" data-bs-toggle="tooltip" title="{{__('dashboard.Clear Filter')}}">
                        <i class="ti-close clear_filter"></i>
                    </a>
                    @endif
                    <!--end::Input group-->
                </div>
                @endif
                <!--end::Actions-->
            </div>
            <!--end::Toolbar container-->
        </div>
        <!--end::Toolbar-->

        <div class="container-fluid" id="app">
            @if (getCurrentEvent())
            <div class="row mt-5 mb-5">
                <div class="col-sm-12 col-md-9 mb-5 overflow-hidden">
                    <div class="card card-bordered">
                        <div class="card-body min-h-300px">
                            <div class="d-flex justify-content-between">
                                <h2>{{__('analytics.Realtime')}}</h2>
                                <div class="">
                                    {{-- @dd(session()->get('analytics_filter')) --}}
                                    <select class="form-select form-select-custome" data-control="select2" data-placeholder="Select one" id="summary_filter">
                                        <option @if(session()->has('analytics_filter') && session()->get('analytics_filter') == 'today') selected @endif value="today">{{__('analytics.Today')}}</option>
                                        <option @if(session()->has('analytics_filter') && session()->get('analytics_filter') == 'this week' || !session()->has('analytics_filter')) selected @endif value="this week">{{__('analytics.This Week')}}</option>
                                        <option @if(session()->has('analytics_filter') && session()->get('analytics_filter') == 'this year') selected @endif value="this year">{{__('analytics.This Year')}}</option>
                                    </select>
                                </div>
                            </div>
                            <!--begin::Chart-->
                            {{-- <div id="kt_apexcharts_4" style="height: 350px;"></div> --}}
                            <canvas id="speedChart" width="600" height="250"></canvas>
                            <!--end::Chart-->

                            {{-- <div class="row">
                                <div class="col-sm-12 col-md-4">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('analytics.Total Viewers')}}</span>
                                            <span class="text-dark" id="total_views"></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-success rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-4">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('analytics.Current Viewers')}}</span>
                                            <span class="text-dark" id="current_views"></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-danger rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-4">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('analytics.Total Logins')}}</span>
                                            <span class="text-dark" id="total_logins"></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-primary rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>

                            </div> --}}
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-md-3">
                    <!--begin::Feeds Widget 4-->
                    <div class="card mb-5">
                        <!--begin::Body-->
                        <div class="card-body pb-0">
                            <p class="mb-5 fw-bold fs-4">{{ __('analytics.Report') }}</p>
                            <!--begin:Form-->
                            <form id="kt_modal_new_target_form" class="form" action="{{route('company.analytics.generateReport')}}" method="POST">
                                @csrf
                                <!--begin::Input group-->
                                <div class="row g-9 mb-8">
                                    <!--begin::Col-->
                                    <div class="col-md-12 fv-row">
                                        <label class="required fs-6 fw-semibold mb-2">{{ __('common.From Date') }}</label>
                                        <!--begin::Input-->
                                        <div class="position-relative d-flex align-items-center">
                                            <!--begin::Icon-->
                                            <!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
                                            <span class="svg-icon svg-icon-2 position-absolute mx-4">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.3"
                                                        d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
                                                        fill="currentColor" />
                                                    <path
                                                        d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
                                                        fill="currentColor" />
                                                    <path
                                                        d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
                                                        fill="currentColor" />
                                                </svg>
                                            </span>
                                            <!--end::Svg Icon-->
                                            <!--end::Icon-->
                                            <!--begin::Datepicker-->
                                            <input class="form-control form-control-solid ps-12" required placeholder="{{__('events.Select a date')}}"
                                                name="from_date" id="from_date" />
                                                <!--end::Datepicker-->
                                            </div>
                                            @error('from_date')
                                            <span class="invalid-feedback d-block" role="alert">
                                                <strong>{{ $message }}</strong>
                                            </span>
                                        @enderror
                                        <!--end::Input-->
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col-md-12 fv-row">
                                        <label class="required fs-6 fw-semibold mb-2">{{ __('common.To Date') }}</label>
                                        <!--begin::Input-->
                                        <div class="position-relative d-flex align-items-center">
                                            <!--begin::Icon-->
                                            <!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
                                            <span class="svg-icon svg-icon-2 position-absolute mx-4">
                                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    xmlns="http://www.w3.org/2000/svg">
                                                    <path opacity="0.3"
                                                        d="M21 22H3C2.4 22 2 21.6 2 21V5C2 4.4 2.4 4 3 4H21C21.6 4 22 4.4 22 5V21C22 21.6 21.6 22 21 22Z"
                                                        fill="currentColor" />
                                                    <path
                                                        d="M6 6C5.4 6 5 5.6 5 5V3C5 2.4 5.4 2 6 2C6.6 2 7 2.4 7 3V5C7 5.6 6.6 6 6 6ZM11 5V3C11 2.4 10.6 2 10 2C9.4 2 9 2.4 9 3V5C9 5.6 9.4 6 10 6C10.6 6 11 5.6 11 5ZM15 5V3C15 2.4 14.6 2 14 2C13.4 2 13 2.4 13 3V5C13 5.6 13.4 6 14 6C14.6 6 15 5.6 15 5ZM19 5V3C19 2.4 18.6 2 18 2C17.4 2 17 2.4 17 3V5C17 5.6 17.4 6 18 6C18.6 6 19 5.6 19 5Z"
                                                        fill="currentColor" />
                                                    <path
                                                        d="M8.8 13.1C9.2 13.1 9.5 13 9.7 12.8C9.9 12.6 10.1 12.3 10.1 11.9C10.1 11.6 10 11.3 9.8 11.1C9.6 10.9 9.3 10.8 9 10.8C8.8 10.8 8.59999 10.8 8.39999 10.9C8.19999 11 8.1 11.1 8 11.2C7.9 11.3 7.8 11.4 7.7 11.6C7.6 11.8 7.5 11.9 7.5 12.1C7.5 12.2 7.4 12.2 7.3 12.3C7.2 12.4 7.09999 12.4 6.89999 12.4C6.69999 12.4 6.6 12.3 6.5 12.2C6.4 12.1 6.3 11.9 6.3 11.7C6.3 11.5 6.4 11.3 6.5 11.1C6.6 10.9 6.8 10.7 7 10.5C7.2 10.3 7.49999 10.1 7.89999 10C8.29999 9.90003 8.60001 9.80003 9.10001 9.80003C9.50001 9.80003 9.80001 9.90003 10.1 10C10.4 10.1 10.7 10.3 10.9 10.4C11.1 10.5 11.3 10.8 11.4 11.1C11.5 11.4 11.6 11.6 11.6 11.9C11.6 12.3 11.5 12.6 11.3 12.9C11.1 13.2 10.9 13.5 10.6 13.7C10.9 13.9 11.2 14.1 11.4 14.3C11.6 14.5 11.8 14.7 11.9 15C12 15.3 12.1 15.5 12.1 15.8C12.1 16.2 12 16.5 11.9 16.8C11.8 17.1 11.5 17.4 11.3 17.7C11.1 18 10.7 18.2 10.3 18.3C9.9 18.4 9.5 18.5 9 18.5C8.5 18.5 8.1 18.4 7.7 18.2C7.3 18 7 17.8 6.8 17.6C6.6 17.4 6.4 17.1 6.3 16.8C6.2 16.5 6.10001 16.3 6.10001 16.1C6.10001 15.9 6.2 15.7 6.3 15.6C6.4 15.5 6.6 15.4 6.8 15.4C6.9 15.4 7.00001 15.4 7.10001 15.5C7.20001 15.6 7.3 15.6 7.3 15.7C7.5 16.2 7.7 16.6 8 16.9C8.3 17.2 8.6 17.3 9 17.3C9.2 17.3 9.5 17.2 9.7 17.1C9.9 17 10.1 16.8 10.3 16.6C10.5 16.4 10.5 16.1 10.5 15.8C10.5 15.3 10.4 15 10.1 14.7C9.80001 14.4 9.50001 14.3 9.10001 14.3C9.00001 14.3 8.9 14.3 8.7 14.3C8.5 14.3 8.39999 14.3 8.39999 14.3C8.19999 14.3 7.99999 14.2 7.89999 14.1C7.79999 14 7.7 13.8 7.7 13.7C7.7 13.5 7.79999 13.4 7.89999 13.2C7.99999 13 8.2 13 8.5 13H8.8V13.1ZM15.3 17.5V12.2C14.3 13 13.6 13.3 13.3 13.3C13.1 13.3 13 13.2 12.9 13.1C12.8 13 12.7 12.8 12.7 12.6C12.7 12.4 12.8 12.3 12.9 12.2C13 12.1 13.2 12 13.6 11.8C14.1 11.6 14.5 11.3 14.7 11.1C14.9 10.9 15.2 10.6 15.5 10.3C15.8 10 15.9 9.80003 15.9 9.70003C15.9 9.60003 16.1 9.60004 16.3 9.60004C16.5 9.60004 16.7 9.70003 16.8 9.80003C16.9 9.90003 17 10.2 17 10.5V17.2C17 18 16.7 18.4 16.2 18.4C16 18.4 15.8 18.3 15.6 18.2C15.4 18.1 15.3 17.8 15.3 17.5Z"
                                                        fill="currentColor" />
                                                </svg>
                                            </span>
                                            <!--end::Svg Icon-->
                                            <!--end::Icon-->
                                            <!--begin::Datepicker-->
                                            <input class="form-control form-control-solid ps-12" placeholder="{{__('events.Select a date')}}" name="to_date" id="to_date" required />
                                            <!--end::Datepicker-->
                                        </div>
                                        @error('to_date')
                                        <span class="invalid-feedback d-block" role="alert">
                                            <strong>{{ $message }}</strong>
                                        </span>
                                    @enderror
                                        <!--end::Input-->
                                    </div>
                                    <!--end::Col-->
                                </div>

                                <input type="hidden" name="webcast" value="{{  base64_decode(request()->get('webcast')) }}">
                                <!--end::Input group-->
                                <!--begin::Actions-->
                                <div class="text-start mb-5">
                                    <button type="submit" id="kt_modal_new_target_submit" class="btn btn-primary">
                                        <span class="indicator-label">{{ __('common.Generate Report') }}</span>

                                    </button>
                                </div>
                                <!--end::Actions-->
                            </form>
                            <!--end:Form-->
                        </div>
                        <!--end::Body-->
                    </div>
                    <!--end::Feeds Widget 4-->

                </div>
                <div class="col-lg-12">
                    <div class="card card-bordered">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-12 col-md-4">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('Total Viewers')}}</span>
                                            <span class="text-dark" id="total_views"></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-success rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-4">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('Current Viewers')}}</span>
                                            <span class="text-dark" id="current_views"></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-danger rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-4">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('analytics.Total Logins')}}</span>
                                            <span class="text-dark" id="total_logins"></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-primary rounded h-8px" role="progressbar" style="width: 72%;" aria-valuenow="50" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-4 mt-15">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('analytics.Total Voters')}}</span>
                                            <span class="text-dark" id="total_voters">{{$total_voters}}</span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-info rounded h-8px" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-12 col-md-4 mt-15">
                                    <div class="d-flex align-items-center flex-column mt-3 w-100 m-w-80">
                                        <div class="d-flex justify-content-between fw-bold fs-6 text-white opacity-75 w-100 mt-auto mb-2">
                                            <span class="text-dark">{{__('Current Voters Weights')}}</span>
                                            <span class="text-dark"><span id="current_weights">0</span> of <span id="total_weights">{{$total_weights}}</span></span>
                                        </div>
                                        <div class="h-8px mx-3 w-100 bg-light bg-opacity-50 rounded">
                                            <div class="bg-warning rounded h-8px" role="progressbar" style="width: 100%;" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="row mt-5 mb-5">
                <div class="col-sm-12 col-md-12">
                    <!--begin::Tables Widget 9-->
                    <div class="card mb-5 mb-xl-8">
                        <!--begin::Header-->
                        <div class="card-header border-0 pt-5">
                            <!--begin::Page title-->
                            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                                <!--begin::Title-->
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('analytics.Current Logged in Users')}}</h1>
                                <!--end::Title-->
                            </div>
                            <!--end::Page title-->
                            <!--begin::Input group-->
                            <div class="position-relative w-md-400px me-md-2 d-none">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                                <span
                                    class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute translate-middle ms-6 top-45">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                            rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
                                        <path
                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                            fill="currentColor" />
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                                <input type="text" class="form-control form-control-solid ps-10" name="search" data-kt-login-table-filter="search"
                                    value="" placeholder="{{__('page.Search by name')}}" />
                                </div>

                                <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                                    
                                </div>
                            </div>
                        <!--end::Header-->
                        <!--begin::Body-->
                        <div class="card-body py-3">
                            <!--begin::Table container-->
                            <div class="table-responsive">
                                <!--begin::Table-->
                                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4" id="currentLoggedinTable">
                                    <!--begin::Table head-->
                                    <thead>
                                        <tr class="fw-bold text-muted">
                                            <th class="min-w-200px">{{__('SL')}}</th>
                                            <th class="min-w-200px">{{__('event_user.First Name')}}</th>
                                            <th class="min-w-200px">{{__('event_user.Last Name')}}</th>
                                            <th class="min-w-150px">{{__('auth.Email')}}</th>
                                            <th class="min-w-150px">{{__('analytics.Last Login')}}</th>
                                            <th class="">{{__('event_user.Session Time')}}</th>
                                            <th class="">{{__('event_user.Current Viewers')}}</th>
                                            <th class="">{{__('event_user.Current Voters')}}</th>
                                            <th class="">{{__('event_user.Current Weight')}}</th>
                                        </tr>
                                    </thead>
                                    <!--end::Table head-->
                                    <!--begin::Table body-->
                                    <tbody></tbody>
                                    <!--end::Table body-->
                                </table>
                                <!--end::Table-->
                            </div>
                            <!--end::Table container-->
                        </div>
                        <!--begin::Body-->
                    </div>
                    <!--end::Tables Widget 9-->
                </div>
            </div>

            <div class="row mt-5 mb-5">
                <div class="col-sm-12 col-md-12">
                    <!--begin::Tables Widget 9-->
                    <div class="card mb-5 mb-xl-8">
                        <!--begin::Header-->
                        <div class="card-header border-0 pt-5">
                            <!--begin::Page title-->
                            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                                <!--begin::Title-->
                                <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">{{__('analytics.Event User Activities')}}</h1>
                                <!--end::Title-->
                            </div>
                            <!--end::Page title-->
                            <!--begin::Input group-->
                            <div class="position-relative w-md-400px me-md-2">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen021.svg-->
                                <span
                                    class="svg-icon svg-icon-3 svg-icon-gray-500 position-absolute translate-middle ms-6 top-45">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none"
                                        xmlns="http://www.w3.org/2000/svg">
                                        <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                            rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
                                        <path
                                            d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                            fill="currentColor" />
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                                <input type="text" class="form-control form-control-solid ps-10" name="search" data-kt-docs-table-filter="search"
                                    value="" placeholder="{{__('page.Search by name')}}" />
                                </div>

                                <div class="d-flex justify-content-end" data-kt-docs-table-toolbar="base">
                                    <div class="d-flex justify-content-between">
                                        <div class="export_btn_div">
                                            <a href="{{ route('company.analytics.exportEventUserActivities') }}" class="me-3 btn btn-light btn-icon-primary btn-text-primary export_pdf"><span class="svg-icon svg-icon-1"><i class="fs-4 m-0 fa-solid fa-file-csv"></i></span>
                                                {{__('common.Export As CSV')}}</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <!--end::Header-->
                        <!--begin::Body-->
                        <div class="card-body py-3">
                            <!--begin::Table container-->
                            <div class="table-responsive">
                                <input type="hidden" id="custom_fileds" value="{{$custom_fileds}}">
                                <!--begin::Table-->
                                <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4" id="datatable">
                                    <!--begin::Table head-->
                                    <thead>
                                        
                                        <tr class="fw-bold text-muted">
                                            <th class="min-w-200px">{{__('event_user.First Name')}}</th>
                                            <th class="min-w-200px">{{__('event_user.Last Name')}}</th>
                                            <th class="min-w-150px">{{__('auth.Email')}}</th>
                                            <th class="min-w-150px">{{$current_event->custom_label_of_membership_number??__('event_user.Membership number')}}</th>
                                            <th class="min-w-150px">{{__('analytics.First Login')}}</th>
                                            <th class="min-w-100px">{{__('analytics.Last Login')}}</th>
                                            <th class="min-w-100px">{{__('event_user.Class')}}</th>
                                            <th class="min-w-100px">{{__('event_user.Phone')}}</th>
                                            @foreach($custom_fileds_label as $custom_filed_label)
                                            <th class="min-w-100px">{{ $custom_filed_label }}</th>
                                            @endforeach
                                        </tr>
                                    </thead>
                                    <!--end::Table head-->
                                    <!--begin::Table body-->
                                    <tbody></tbody>
                                    <!--end::Table body-->
                                </table>
                                <!--end::Table-->
                            </div>
                            <!--end::Table container-->
                        </div>
                        <!--begin::Body-->
                    </div>
                    <!--end::Tables Widget 9-->
                </div>
            </div>
            @else
            <div class="row">
                <div class="col-xl-12 ">
                    <h2 class="empty_microsite">
                        {{__('webcast.Create an event first.')}}
                    </h2>
                </div>
            </div>
        @endif
        </div>
        <input type="hidden" id="days" value="{{json_encode($days)}}">
        <input type="hidden" id="months" value="{{json_encode($months)}}">
    </div>
    @php
        $request_webcast = null;
        if(request()->get('webcast')){
            $request_webcast = (int)base64_decode(request()->get('webcast'));
        }elseif($selected_webcast){
            $request_webcast = (int)@$selected_webcast;
        }
    @endphp
@endsection

@push('vue_push')
@vite(['resources/sass/app.scss', 'resources/js/app.js'])
@endpush

@push('scripts')
<script src="https://momentjs.com/downloads/moment-with-locales.js"></script>
    <script src="https://momentjs.com/downloads/moment-timezone-with-data.js"></script>
    <script>
        $(document).ready(function(){
            var current_lang = "{{auth()->user()->lang_code}}";

            $(document).on('change', '#webcast_list', function(){
                let webcast = $(this).val();
                if(webcast){
                    location.href = "{{url('company/analytics')}}"+"?webcast="+webcast;
                }
            });
            selectWeb();
            function selectWeb(){
                var id = $('#webcast_list').val();
                var clear_webcast = "{{session()->get('selectWebcast')}}";
                if(id && !clear_webcast){
                    // var url = "{{url('company/analytics')}}"+"?webcast="+id;
                    // history.replaceState(null, '', url);
                    var currentUrl = window.location.href;
                    var url = new URL(currentUrl);
                    url.searchParams.set('webcast', id);
                    var newUrl = url.toString();
                    history.replaceState({ path: newUrl }, '', newUrl);
                }
            }

            $(document).on('click', '.data_delete', function(e){
                e.preventDefault();
                $.get('/company/analytics/remove-webcast', function(res){
                    location.href = "{{url('company/analytics')}}";
                });
            });
            // dataTable('#datatable', '[data-kt-user-table-filter="search"]');

            var KTDatatablesServerSide = function () {
                // Shared variables
                var table;
                var dt;
                var filterPayment;

                // Private functions
                var initDatatable = function () {

                    var custom_fileds = $('#custom_fileds').val();
                    custom_fileds = JSON.parse(custom_fileds);

                    var column_data = [
                        // { data: 'DT_RowIndex',name:'id' },
                        { data: 'first_name', name:'first_name' },
                        { data: 'last_name', name:'last_name' },
                        { data: 'email', name:'email' },
                        { data: 'membership_number', name:'membership_number' },
                        { data: 'created_at', name:'created_at' },
                        { data: 'updated_at', name:'updated_at' },
                        { data: 'class', name:'class' },
                        { data: 'phone', name:'phone' },
                    ];

                    custom_fileds.forEach((val) => {
                        var c_column = {data:val+"-(custom)", name:val+"-(custom)"};
                        column_data.push(c_column);
                    });

                    dt = $("#datatable").DataTable({
                        searchDelay: 500,
                        processing: true,
                        serverSide: true,
                        "language": {
                            "emptyTable": "{{ __('common.No user available') }}",
                            "info": "{{ __('common.Viewing') }} _START_ {{ __('common.to') }} _END_ {{ __('common.of') }} _TOTAL_ {{ strtolower(__('event_user.registrants')) }}"
                        },
                        info: true,
                        stateSave: true,
                        select: {
                            style: 'multi',
                            selector: 'td:first-child input[type="checkbox"]',
                            className: 'row-selected'
                        },
                        ajax: {
                            url: "{{route('company.analytics.get-user-activities')}}"
                        },
                        columns: column_data,
                        columnDefs: [
                            {
                                targets: 0,
                                orderable: false
                            },
                        ],
                        // Add data-filter attribute
                        createdRow: function (row, data, dataIndex) {
                            $(row).find('td:eq(2)').html('<a href="mailto:' + $(row).find('td:eq(2)').text() + '">' + $(row).find('td:eq(2)').text() + '</a>');
                            $(row).find('td:eq(4)').attr('data-filter', data.CreditCardType);
                        }
                    });

                    table = dt.$;

                    // Re-init functions on every table re-draw -- more info: https://datatables.net/reference/event/draw
                    dt.on('draw', function () {
                        // initToggleToolbar();
                        // toggleToolbars();
                        KTMenu.createInstances();
                    });
                }

                // Search Datatable --- official docs reference: https://datatables.net/reference/api/search()
                var handleSearchDatatable = function () {
                    const filterSearch = document.querySelector('[data-kt-docs-table-filter="search"]');
                    filterSearch.addEventListener('keyup', function (e) {
                        dt.search(e.target.value).draw();
                    });
                }

                // Init toggle toolbar
                var initToggleToolbar = function () {
                    // Toggle selected action toolbar
                    // Select all checkboxes
                    const container = document.querySelector('#kt_datatable_example_1');
                    const checkboxes = container.querySelectorAll('.parent_checkbox');

                    // Toggle delete selected toolbar
                    checkboxes.forEach(c => {
                        // Checkbox on click event
                        c.addEventListener('click', function () {
                            setTimeout(function () {
                                toggleToolbars();
                            }, 50);
                        });
                    });
                }

                // Toggle toolbars
                var toggleToolbars = function () {
                    // Define variables
                    const container = document.querySelector('#kt_datatable_example_1');
                    const toolbarBase = document.querySelector('[data-kt-docs-table-toolbar="base"]');
                    const toolbarSelected = document.querySelector('[data-kt-docs-table-toolbar="selected"]');
                    const selectedCount = document.querySelector('[data-kt-docs-table-select="selected_count"]');

                    // Select refreshed checkbox DOM elements
                    const allCheckboxes = container.querySelectorAll('tbody .selector');

                    // Detect checkboxes state & count
                    let checkedState = false;
                    let count = 0;

                    // Count checked boxes
                    allCheckboxes.forEach(c => {
                        if (c.checked) {
                            checkedState = true;
                            count++;
                        }
                    });

                    // Toggle toolbars
                    if (checkedState) {
                        selectedCount.innerHTML = count;
                        toolbarBase.classList.add('d-none');
                        toolbarSelected.classList.remove('d-none');
                    } else {
                        toolbarBase.classList.remove('d-none');
                        toolbarSelected.classList.add('d-none');
                    }
                }

                // Public methods
                return {
                    init: function () {
                        initDatatable();
                        handleSearchDatatable();
                        // initToggleToolbar();
                    }
                }
            }();

            KTUtil.onDOMContentLoaded(function () {
                KTDatatablesServerSide.init();
            });

            checkWebsocketForWebcastReload();
            function checkWebsocketForWebcastReload(){
                let event_id = "{{@$current_event->id}}";
				window.Echo.private(`event-user-login-activity-channel-${event_id}`)
                .listen('EventUserLoginActivity', (e) => {
                    views('new');
                });
			}


            $("#from_date").flatpickr({
                dateFormat: "Y-m-d",
            });
            $("#to_date").flatpickr({
                dateFormat: "Y-m-d",
            });
            function checkWebsocketForJoinToWebcast(){
                let webcast = "{{$request_webcast}}";
                if(webcast){
                    window.Echo.private(`event-user-view-webcast-channel-${webcast}`)
                    .listen('viewWebcast', (e) => {
                        views('new');
                    });
                }else{
                    let event = "{{@getCurrentEvent()->id}}";
                    window.Echo.private(`event-user-view-event-channel-${event}`)
                    .listen('viewEvent', (e) => {
                        views('new');
                    });
                }
            }
            checkWebsocketForJoinToWebcast();
            checkWebsocketForLeaveFromWebcast();

            function checkWebsocketForLeaveFromWebcast(){
                let webcast = "{{$request_webcast}}";
                if(webcast){
                    window.Echo.private(`event-user-leave-webcast-channel-${parseInt(webcast)}`)
                    .listen('leaveWebcast', (e) => {
                        views('new');
                    });
                }else{
                    let event = "{{@getCurrentEvent()->id}}";
                    window.Echo.private(`event-user-leave-event-channel-${parseInt(event)}`)
                    .listen('leaveEvent', (e) => {
                        views('new');
                    });
                }


            }

            let globalCurrentViewsData = [];
            function views(initType='new'){
                let webcast = "{{$request_webcast}}";
                let filter = $('#summary_filter').val();
                if(webcast){
                    let data = {
                        _token: "{{csrf_token()}}",
                        type:'webcast',
                        id:webcast,
                        filter: filter
                    };
                    $.post('/company/analytics/get-viewers', data, function(res){
                        $('#total_views').text(res.total_views);
                        $('#current_views').text(res.current_views);
                        $('#total_logins').text(res.total_logins);
                        $('#current_weights').text(res.current_weights);
                        if(initType == 'new'){
                            initChart(filter,res.month_wise_total_views, res.month_wise_total_logins, res.current_views);
                        }

                        globalCurrentViewsData = res.current_viewers;
                    });
                }else{
                    let data = {
                        _token: "{{csrf_token()}}",
                        type:'event',
                        id:"{{@getCurrentEvent()->id}}",
                        filter: filter
                    };


                    $.post('/company/analytics/get-viewers', data, function(res){
                        $('#total_views').text(res.total_views);
                        $('#current_views').text(res.current_views);
                        $('#total_logins').text(res.total_logins);
                        $('#current_weights').text(res.current_weights);
                        if(initType == 'new'){
                            initChart(filter,res.month_wise_total_views, res.month_wise_total_logins, res.current_views);
                        }
                        
                        globalCurrentViewsData = res.current_viewers;
                    });
                }

            }
            views();

            $(document).on('change', '#summary_filter', function(){
                let data = {
                    _token: "{{csrf_token()}}",
                    type:'event',
                    value: $(this).val()
                };

                $.post('/company/analytics/filter-summary', data, function(res){
                    location.reload();
                });
            });

            String.prototype.toHHMMSS = function () {
                var sec_num = parseInt(this, 10); // don't forget the second param
                var hours   = Math.floor(sec_num / 3600);
                var minutes = Math.floor((sec_num - (hours * 3600)) / 60);
                var seconds = sec_num - (hours * 3600) - (minutes * 60);

                if (hours   < 10) {hours   = "0"+hours;}
                if (minutes < 10) {minutes = "0"+minutes;}
                if (seconds < 10) {seconds = "0"+seconds;}
                return hours+':'+minutes+':'+seconds;
            }
            async function currentLoggedinTable(data)
            {
                let currentViewsData = globalCurrentViewsData;
                
                    // Reference to the table body element
                var tableBody = document.getElementById("currentLoggedinTable");

                var i = 1;
                await data.forEach((user) => {
                    if(user.role_id != 3){
                        // Create a new row element
                        var row = document.createElement("tr");
                        row.id = "currentLoggedinUser-"+ user.id;

                        var idcell = document.createElement("td");
                        
                        idcell.textContent = i;
                        row.appendChild(idcell);
                        // Create table cells and populate them with data
                        var firstNameCell = document.createElement("td");
                        firstNameCell.textContent = user.first_name??'';
                        row.appendChild(firstNameCell);
                        var lastNameCell = document.createElement("td");
                        lastNameCell.textContent = user.last_name??'';
                        row.appendChild(lastNameCell);

                        var email = document.createElement("td");
                        var linkNode = document.createElement('a');
                        email.appendChild(linkNode);
                        var linkText = document.createTextNode(user.email);
                        linkNode.appendChild(linkText);
                        linkNode.title = user.email;
                        linkNode.href = "mailto:" + user.email;
                        row.appendChild(email);
                        var loginTimeCell = document.createElement("td");
                        loginTimeCell.textContent = moment.tz(user.login_activity.updated_at, window.env_tz).locale(current_lang).format('HH:mm:ss');
                        row.appendChild(loginTimeCell);
                        var sessionTime = document.createElement("td");
                        var current_time = moment().tz(window.env_tz);
                        var server_time = moment.tz(user.login_activity.updated_at, window.env_tz);
                        var duration = moment.duration(current_time.diff(server_time));
                        duration = duration.asSeconds();
                        duration = duration.toString();
                        sessionTime.textContent = duration.toHHMMSS();
                        row.appendChild(sessionTime);

                        var currentViewers = document.createElement("td");
                        let isViewer = currentViewsData?.some(value => value.event_user_id == user.id);
                        currentViewers.textContent = isViewer ? 'Yes' : 'No';
                        row.appendChild(currentViewers);

                        var currentVoters = document.createElement("td");
                        currentVoters.textContent = user.voter ?? '';
                        row.appendChild(currentVoters);

                        var currentWeight = document.createElement("td");
                        if (user.weight) {
                            try {
                                // Parse the JSON string
                                var weightObj = JSON.parse(user.weight);
                                // Format the output in a more readable way
                                var readableWeight = Object.entries(weightObj)
                                    .map(([key, value]) => `${key}: ${value}`)
                                    .join(', ');
                                currentWeight.textContent = readableWeight;
                            } catch (e) {
                                // If parsing fails, show the original string or handle the error
                                currentWeight.textContent = user.weight;
                            }
                        } else {
                            currentWeight.textContent = '';
                        }
                        row.appendChild(currentWeight);

                        // Append the row to the table body
                        $("#currentLoggedinTable").find('tbody').append(row);
                        i += 1;
                    }
                });

                await dataTable('#currentLoggedinTable', '[data-kt-login-table-filter="search"]',"{{ __('common.No user available') }}");
            }

            async function currentLoggedinTableJoining(data)
            {
                var tableBody = document.getElementById("currentLoggedinTable");

                if($("#currentLoggedinTable").find('tbody tr:last td:first').text()){
                    var sl = $("#currentLoggedinTable").find('tbody tr:last td:first').text();
                    if(sl == 'No user available'){
                        sl = 1;
                    }else{
                        sl = await parseInt($("#currentLoggedinTable").find('tbody tr:last td:first').text()) + 1;
                    }
                }else{
                    var sl = 1;
                }
                var current_time = await moment().tz(window.env_tz);
                var server_time = await moment.tz(data.login_activity.updated_at, window.env_tz);
                var duration = await moment.duration(current_time.diff(server_time));
                duration = await duration.asSeconds();
                duration = await duration.toString();
                sessionTime = await duration.toHHMMSS().toString();

                // Check for viewer data
                let currentViewsData = globalCurrentViewsData;  // Assuming globalCurrentViewsData is available
                let isViewer = currentViewsData.some(value => value.event_user_id == data.id);
                let currentViewers = isViewer ? 'Yes' : 'No';

                // Check voter status
                let currentVoter = data.voter ?? '';

                // Parse and format weight data
                let currentWeight = '';
                if (data.weight) {
                    try {
                        var weightObj = JSON.parse(data.weight);
                        currentWeight = Object.entries(weightObj).map(([key, value]) => `${key}: ${value}`).join(', ');
                    } catch (e) {
                        currentWeight = data.weight;  // Fallback to raw value if JSON parse fails
                    }
                }

                // await $("#currentLoggedinTable").find('tbody').append(`
                //     <tr id ="currentLoggedinUser-${data.id}" >
                //         <td>${sl}</td>
                //         <td>${data.name}</td>
                //         <td>${data.email}</td>
                //         <td>${moment.tz(data.login_activity.updated_at, window.env_tz).format('h:mm:ss a')}</td>
                //         <td>${sessionTime}</td>
                //     </tr>
                // `);
                var tt = moment.tz(data.login_activity.updated_at, window.env_tz).locale(current_lang).format('HH:mm:ss').toString();
                // var datas = [sl, data.name, data.email, tt, sessionTime];
                var hh = $('#currentLoggedinTable').DataTable();
                // hh.row.add(datas).node().id = "currentLoggedinUser-"+data.id;
                var first_name = data.first_name??'';
                var last_name = data.last_name??'';
                var email = '<a href="mailto:' + data.email + '">' + data.email + '</a>'
                hh.row.add(
                    $('<tr>'+
                        '<td>'+sl+'</td>'+
                        '<td>'+first_name+'</td>'+
                        '<td>'+last_name+'</td>'+
                        '<td>'+email+'</td>'+
                        '<td>'+tt+'</td>'+
                        '<td>'+sessionTime+'</td>'+
                        '<td>' + currentViewers + '</td>' +  // Add Current Viewers column
                        '<td>' + currentVoter + '</td>' +    // Add Current Voter column
                        '<td>' + currentWeight + '</td>' +   // Add Current Weight column
                    '</tr>')[0]
                ).node().id = "currentLoggedinUser-"+data.id;
                hh.draw(true);

                // $('#currentLoggedinTable').DataTable().row.add(datas).draw();
                //  rowNode.add('')
                // await dataTable('#currentLoggedinTable', '[data-kt-login-table-filter="search"]',"{{ __('common.No user available') }}");
                // await $('#currentLoggedinTable').DataTable().draw();
            }
            //for test channel
            function checkWebsocketForTestPre(){
                let event_id = "{{@getCurrentEvent()->id}}";
                let webcast = "{{$request_webcast}}";
                if(webcast){
                    window.Echo.join('webcast-presence-channel-'+webcast)
                        .here((users) => {
                            var myId = "{{auth()->id()}}";
                            users = users.filter(user => user.id !== parseInt(myId));
                            var current_user_ids = users.map(({ id }) => id);

                            var data = {
                                _token: "{{csrf_token()}}",
                                type:'webcast',
                                eventable_id: webcast,
                                users: current_user_ids
                            };
                            $.post('/company/analytics/remove-expire-views', data, function(res){
                                views('new');
                            });
                            
                            currentLoggedinTable(users);
                            $('#current_views').html((users.length));
                        })
                        .joining((res) => {
                            currentLoggedinTableJoining(res);
                        })
                        .leaving((user) => {
                            var data = {
                                _token: "{{csrf_token()}}",
                                type:'webcast',
                                eventable_id: webcast,
                                user: user.id
                            };
                            $('#currentLoggedinTable').DataTable().row( '#currentLoggedinUser-'+user.id ).remove().draw();
                            $.post('/company/analytics/leaving-user-remove-from-current-view', data, function(res){
                                views('new');
                            });
                        });
                }else{
                    window.Echo.join('event-presence-channel-'+event_id)
                        .here((users) => {
                            var myId = "{{auth()->id()}}";
                            users = users.filter(user => user.id !== parseInt(myId));
                            var current_user_ids = users.map(({ id }) => id);
                            let data = {
                                _token: "{{csrf_token()}}",
                                type:'event',
                                eventable_id: event_id,
                                users: current_user_ids
                            };
                            $.post('/company/analytics/remove-expire-views', data, function(res){
                                views('new');
                            });
                            currentLoggedinTable(users);
                            $('#current_views').html((users.length));
                        })
                        .joining((res) => {
                            currentLoggedinTableJoining(res);
                        })
                        .leaving((user) => {
                            let data = {
                                _token: "{{csrf_token()}}",
                                type:'event',
                                eventable_id: event_id,
                                user: user.id
                            };

                            $('#currentLoggedinTable').DataTable().row( '#currentLoggedinUser-'+user.id ).remove().draw();

                            $.post('/company/analytics/leaving-user-remove-from-current-view', data, function(res){
                                views('new');
                            });
                        });
                }

            }

            checkWebsocketForTestPre();

            let lineChart;
            function initChart(filter,get_total_views, get_total_logins, current_viewer_count=null){
                var speedCanvas = document.getElementById("speedChart");

                if(filter == 'this year'){
                    let total_views = [0,0,0,0,0,0,0,0,0,0,0,0];
                    let total_logins = [0,0,0,0,0,0,0,0,0,0,0,0];
                    let current_logins = [];
                    let months = JSON.parse($('#months').val());
                    let s_datas = ["@lang('analytics.January')", "@lang('analytics.February')", "@lang('analytics.March')", "@lang('analytics.April')", "@lang('analytics.May')", "@lang('analytics.June')", "@lang('analytics.July')", "@lang('analytics.Auguest')", "@lang('analytics.September')", "@lang('analytics.October')", "@lang('analytics.November')", "@lang('analytics.December')"];

                    get_total_views.forEach((e) => {
                        var t = s_datas.indexOf(months[e.month])
                        total_views[t] = e.total;
                    });
                    get_total_logins.forEach((e) => {
                        var t = s_datas.indexOf(months[e.month])
                        total_logins[t] = e.total;
                    });

                    var c_time = moment().tz(env_tz).format('MMMM');
                    c_time = months[c_time];
                    s_datas.forEach((val) => {
                        if(c_time == val){
                            current_logins.push(current_viewer_count);
                        }else{
                            current_logins.push(0);
                        }
                    });

                    var dataFirst = {
                        label: "@lang('analytics.Total Viewers')",
                        data: total_views,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#50cd89'
                    };

                    var dataSecond = {
                        label: "@lang('analytics.Total Logins')",
                        data: total_logins,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#009ef7'
                    };
                    var dataThird = {
                        label: "@lang('analytics.Current Viewers')",
                        data: current_logins,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#f1416c'
                    };

                    var speedData = {
                        labels: s_datas,
                        datasets: [dataFirst, dataSecond, dataThird]
                    };

                    var chartOptions = {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                            boxWidth: 80,
                            fontColor: 'black'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero:true
                            }
                        }
                    };

                    if(lineChart){
                        lineChart.destroy();
                        lineChart = new Chart(speedCanvas, {
                            type: 'line',
                            data: speedData,
                            options: chartOptions
                        });
                    }else{
                        lineChart = new Chart(speedCanvas, {
                            type: 'line',
                            data: speedData,
                            options: chartOptions
                        });
                    }
                }else if(filter == 'today'){
                    let total_views = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
                    let total_logins = [0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];
                    let current_logins = [];
                    let s_datas = [1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24];

                    get_total_views.forEach((e) => {
                        var t = s_datas.indexOf(e.hour);
                        total_views[t] = e.total;
                    });
                    get_total_logins.forEach((e) => {
                        var t = s_datas.indexOf(e.hour)
                        total_logins[t] = e.total;
                    });

                    var c_time = moment().tz(env_tz).hour();
                    s_datas.forEach((val) => {
                        if(c_time == val){
                            current_logins.push(current_viewer_count);
                        }else{
                            current_logins.push(0);
                        }
                    });

                    var dataFirst = {
                        label: "@lang('analytics.Total Viewers')",
                        data: total_views,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#50cd89'
                    };

                    var dataSecond = {
                        label: "@lang('analytics.Total Logins')",
                        data: total_logins,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#009ef7'
                    };
                    var dataThird = {
                        label: "@lang('analytics.Current Viewers')",
                        data: current_logins,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#f1416c'
                    };

                    var speedData = {
                        labels: s_datas,
                        datasets: [dataFirst, dataSecond, dataThird]
                    };

                    var chartOptions = {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                            boxWidth: 80,
                            fontColor: 'black'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero:true
                            }
                        }
                    };

                    if(lineChart){
                        lineChart.destroy();
                        lineChart = new Chart(speedCanvas, {
                            type: 'line',
                            data: speedData,
                            options: chartOptions
                        });
                    }else{
                        lineChart = new Chart(speedCanvas, {
                            type: 'line',
                            data: speedData,
                            options: chartOptions
                        });
                    }
                }else{
                    let days = JSON.parse($('#days').val());
                    let total_views = [0,0,0,0,0,0,0];
                    let total_logins = [0,0,0,0,0,0,0];
                    let current_logins = [];
                    let s_datas = ["@lang('analytics.Saturday')", "@lang('analytics.Sunday')", "@lang('analytics.Monday')", "@lang('analytics.Tuesday')", "@lang('analytics.Wednesday')", "@lang('analytics.Thursday')", "@lang('analytics.Friday')"];

                    get_total_views.forEach((e) => {
                        var t = s_datas.indexOf(days[e.day]);
                        total_views[t] = e.total;
                    });
                    get_total_logins.forEach((e) => {
                        var t = s_datas.indexOf(days[e.day])
                        total_logins[t] = e.total;
                    });

                    var c_time = moment().tz(env_tz).format('dddd');
                    c_time = days[c_time];
                    s_datas.forEach((val) => {
                        if(c_time == val){
                            current_logins.push(current_viewer_count);
                        }else{
                            current_logins.push(0);
                        }
                    });

                    var dataFirst = {
                        label: "@lang('analytics.Total Viewers')",
                        data: total_views,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#50cd89'
                    };

                    var dataSecond = {
                        label: "@lang('analytics.Total Logins')",
                        data: total_logins,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#009ef7'
                    };
                    var dataThird = {
                        label: "@lang('analytics.Current Viewers')",
                        data: current_logins,
                        lineTension: 0,
                        fill: false,
                        borderColor: '#f1416c'
                    };

                    var speedData = {
                        labels: s_datas,
                        datasets: [dataFirst, dataSecond, dataThird]
                    };

                    var chartOptions = {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                            boxWidth: 80,
                            fontColor: 'black'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero:true
                            }
                        }
                    };

                    if(lineChart){
                        lineChart.destroy();
                        lineChart = new Chart(speedCanvas, {
                            type: 'line',
                            data: speedData,
                            options: chartOptions
                        });
                    }else{
                        lineChart = new Chart(speedCanvas, {
                            type: 'line',
                            data: speedData,
                            options: chartOptions
                        });
                    }
                }
            }

        });
    </script>
@endpush

@push('styles')
    <style>
        .top-45{
            top: 45%!important;
        }
        #kt_app_body{
            overflow: scroll!important;
        }
        .m-w-80{
            max-width: 80%;
        }
    </style>
@endpush
